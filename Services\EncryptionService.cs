using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace PasswordManager.Services
{
    /// <summary>
    /// Provides encryption and decryption services using AES-256 with optimized security
    /// </summary>
    public static class EncryptionService
    {
        private const int KeySize = 256; // AES-256
        private const int BlockSize = 128;
        private const int SaltSize = 32; // Increased to 256 bits for better security
        private const int IvSize = 16; // 128 bits
        private const int Iterations = 100000; // Increased PBKDF2 iterations for better security

        // P/Invoke for secure memory operations (Windows only)
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool VirtualLock(IntPtr lpAddress, UIntPtr dwSize);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool VirtualUnlock(IntPtr lpAddress, UIntPtr dwSize);

        [DllImport("kernel32.dll")]
        private static extern void RtlZeroMemory(IntPtr dest, UIntPtr size);

        /// <summary>
        /// Encrypts a password using AES-256 with a master password
        /// </summary>
        /// <param name="plainText">The password to encrypt</param>
        /// <param name="masterPassword">The master password for encryption</param>
        /// <returns>Tuple containing encrypted data and salt</returns>
        public static (string encryptedData, string salt) EncryptPassword(string plainText, string masterPassword)
        {
            if (string.IsNullOrEmpty(plainText))
                throw new ArgumentException("Plain text cannot be null or empty", nameof(plainText));

            if (string.IsNullOrEmpty(masterPassword))
                throw new ArgumentException("Master password cannot be null or empty", nameof(masterPassword));

            try
            {
                // Generate random salt
                byte[] salt = GenerateRandomBytes(SaltSize);

                // Derive key from master password using PBKDF2
                byte[] key = DeriveKey(masterPassword, salt);

                // Generate random IV
                byte[] iv = GenerateRandomBytes(IvSize);

                // Encrypt the data
                byte[] encryptedBytes;
                using (var aes = Aes.Create())
                {
                    aes.KeySize = KeySize;
                    aes.BlockSize = BlockSize;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.Key = key;
                    aes.IV = iv;

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    {
                        // Write IV to the beginning of the stream
                        msEncrypt.Write(iv, 0, iv.Length);

                        using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                        using (var swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }
                        encryptedBytes = msEncrypt.ToArray();
                    }
                }

                // Return base64 encoded encrypted data and salt
                return (Convert.ToBase64String(encryptedBytes), Convert.ToBase64String(salt));
            }
            catch (Exception ex)
            {
                throw new Exception($"Encryption failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Decrypts a password using AES-256 with a master password
        /// </summary>
        /// <param name="encryptedData">The encrypted data (base64)</param>
        /// <param name="salt">The salt used for encryption (base64)</param>
        /// <param name="masterPassword">The master password for decryption</param>
        /// <returns>The decrypted password</returns>
        public static string DecryptPassword(string encryptedData, string salt, string masterPassword)
        {
            if (string.IsNullOrEmpty(encryptedData))
                throw new ArgumentException("Encrypted data cannot be null or empty", nameof(encryptedData));

            if (string.IsNullOrEmpty(salt))
                throw new ArgumentException("Salt cannot be null or empty", nameof(salt));

            if (string.IsNullOrEmpty(masterPassword))
                throw new ArgumentException("Master password cannot be null or empty", nameof(masterPassword));

            try
            {
                // Convert from base64
                byte[] encryptedBytes = Convert.FromBase64String(encryptedData);
                byte[] saltBytes = Convert.FromBase64String(salt);

                // Derive key from master password using the same salt
                byte[] key = DeriveKey(masterPassword, saltBytes);

                // Extract IV from the beginning of encrypted data
                byte[] iv = new byte[IvSize];
                Array.Copy(encryptedBytes, 0, iv, 0, IvSize);

                // Extract the actual encrypted content
                byte[] cipherText = new byte[encryptedBytes.Length - IvSize];
                Array.Copy(encryptedBytes, IvSize, cipherText, 0, cipherText.Length);

                // Decrypt the data
                string plainText;
                using (var aes = Aes.Create())
                {
                    aes.KeySize = KeySize;
                    aes.BlockSize = BlockSize;
                    aes.Mode = CipherMode.CBC;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.Key = key;
                    aes.IV = iv;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(cipherText))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var srDecrypt = new StreamReader(csDecrypt))
                    {
                        plainText = srDecrypt.ReadToEnd();
                    }
                }

                return plainText;
            }
            catch (Exception ex)
            {
                throw new Exception($"Decryption failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Generates a hash of the master password for verification
        /// </summary>
        /// <param name="masterPassword">The master password to hash</param>
        /// <param name="salt">Optional salt (if null, a new one is generated)</param>
        /// <returns>Tuple containing the hash and salt</returns>
        public static (string hash, string salt) HashMasterPassword(string masterPassword, string salt = null)
        {
            if (string.IsNullOrEmpty(masterPassword))
                throw new ArgumentException("Master password cannot be null or empty", nameof(masterPassword));

            try
            {
                byte[] saltBytes;
                if (string.IsNullOrEmpty(salt))
                {
                    saltBytes = GenerateRandomBytes(SaltSize);
                }
                else
                {
                    saltBytes = Convert.FromBase64String(salt);
                }

                // Use PBKDF2 to hash the password
                using (var pbkdf2 = new Rfc2898DeriveBytes(masterPassword, saltBytes, Iterations, HashAlgorithmName.SHA256))
                {
                    byte[] hash = pbkdf2.GetBytes(32); // 256 bits
                    return (Convert.ToBase64String(hash), Convert.ToBase64String(saltBytes));
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Password hashing failed: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Verifies a master password against a stored hash
        /// </summary>
        /// <param name="masterPassword">The password to verify</param>
        /// <param name="storedHash">The stored hash</param>
        /// <param name="salt">The salt used for the hash</param>
        /// <returns>True if the password is correct</returns>
        public static bool VerifyMasterPassword(string masterPassword, string storedHash, string salt)
        {
            try
            {
                var (computedHash, _) = HashMasterPassword(masterPassword, salt);
                return computedHash == storedHash;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Derives an encryption key from a password using PBKDF2
        /// </summary>
        private static byte[] DeriveKey(string password, byte[] salt)
        {
            using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations, HashAlgorithmName.SHA256))
            {
                return pbkdf2.GetBytes(KeySize / 8); // Convert bits to bytes
            }
        }

        /// <summary>
        /// Generates cryptographically secure random bytes
        /// </summary>
        private static byte[] GenerateRandomBytes(int size)
        {
            byte[] bytes = new byte[size];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }
            return bytes;
        }

        /// <summary>
        /// Generates a secure random password
        /// </summary>
        /// <param name="length">Length of the password</param>
        /// <param name="includeSymbols">Whether to include symbols</param>
        /// <returns>A randomly generated password</returns>
        public static string GenerateSecurePassword(int length = 16, bool includeSymbols = true)
        {
            const string lowercase = "abcdefghijklmnopqrstuvwxyz";
            const string uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            const string digits = "0123456789";
            const string symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";

            string chars = lowercase + uppercase + digits;
            if (includeSymbols)
                chars += symbols;

            var password = new StringBuilder();
            using (var rng = RandomNumberGenerator.Create())
            {
                byte[] randomBytes = new byte[length];
                rng.GetBytes(randomBytes);

                for (int i = 0; i < length; i++)
                {
                    password.Append(chars[randomBytes[i] % chars.Length]);
                }
            }

            return password.ToString();
        }

        /// <summary>
        /// Securely clears a string from memory (best effort)
        /// </summary>
        public static void SecureClearString(ref string sensitiveString)
        {
            if (sensitiveString != null)
            {
                // Note: This is a best effort approach. In .NET, strings are immutable
                // and may remain in memory. For truly secure applications, consider
                // using SecureString or char arrays that can be explicitly cleared.
                sensitiveString = null;
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
            }
        }

        /// <summary>
        /// Encrypts data for local storage using machine-specific key
        /// </summary>
        [System.Runtime.Versioning.SupportedOSPlatform("windows")]
        public static string EncryptForStorage(string plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return string.Empty;

            try
            {
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                byte[] encryptedBytes = ProtectedData.Protect(plainBytes, null, DataProtectionScope.CurrentUser);
                return Convert.ToBase64String(encryptedBytes);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to encrypt data for storage: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Decrypts data from local storage using machine-specific key
        /// </summary>
        [System.Runtime.Versioning.SupportedOSPlatform("windows")]
        public static string DecryptFromStorage(string encryptedText)
        {
            if (string.IsNullOrEmpty(encryptedText))
                return string.Empty;

            try
            {
                byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                byte[] plainBytes = ProtectedData.Unprotect(encryptedBytes, null, DataProtectionScope.CurrentUser);
                return Encoding.UTF8.GetString(plainBytes);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to decrypt data from storage: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Secure memory management for sensitive data
        /// </summary>
        public sealed class SecureMemory : IDisposable
        {
            private GCHandle _handle;
            private byte[] _data;
            private bool _disposed = false;

            public SecureMemory(int size)
            {
                _data = new byte[size];
                _handle = GCHandle.Alloc(_data, GCHandleType.Pinned);

                if (OperatingSystem.IsWindows())
                {
                    VirtualLock(_handle.AddrOfPinnedObject(), (UIntPtr)size);
                }
            }

            public byte[] Data => _data;
            public IntPtr Address => _handle.AddrOfPinnedObject();

            public void Dispose()
            {
                if (!_disposed)
                {
                    if (OperatingSystem.IsWindows())
                    {
                        RtlZeroMemory(_handle.AddrOfPinnedObject(), (UIntPtr)_data.Length);
                        VirtualUnlock(_handle.AddrOfPinnedObject(), (UIntPtr)_data.Length);
                    }
                    else
                    {
                        Array.Clear(_data, 0, _data.Length);
                    }

                    _handle.Free();
                    _disposed = true;
                }
            }
        }


    }
}
