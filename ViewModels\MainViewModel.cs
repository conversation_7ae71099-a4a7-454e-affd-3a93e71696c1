using System;
using System.Windows.Input;
using PasswordManager.Services;
using PasswordManager.Utilities;

namespace PasswordManager.ViewModels
{
    /// <summary>
    /// Main ViewModel for the application
    /// </summary>
    public class MainViewModel : BaseViewModel
    {
        private AccountService _accountService;

        #region Properties

        private SaveAccountViewModel _saveAccountViewModel;
        public SaveAccountViewModel SaveAccountViewModel
        {
            get => _saveAccountViewModel;
            set => SetProperty(ref _saveAccountViewModel, value);
        }

        private int _selectedTabIndex;
        public int SelectedTabIndex
        {
            get => _selectedTabIndex;
            set => SetProperty(ref _selectedTabIndex, value);
        }

        private string _applicationTitle;
        public string ApplicationTitle
        {
            get => _applicationTitle;
            set => SetProperty(ref _applicationTitle, value);
        }

        private bool _isAuthenticated;
        public bool IsAuthenticated
        {
            get => _isAuthenticated;
            set => SetProperty(ref _isAuthenticated, value);
        }

        private string _masterPassword;
        public string MasterPassword
        {
            get => _masterPassword;
            set => SetProperty(ref _masterPassword, value, () =>
            {
                // Notify that the AuthenticateCommand's CanExecute state may have changed
                System.Windows.Input.CommandManager.InvalidateRequerySuggested();
            });
        }

        private bool _rememberMasterPassword;
        public bool RememberMasterPassword
        {
            get => _rememberMasterPassword;
            set => SetProperty(ref _rememberMasterPassword, value);
        }

        #endregion

        #region Commands

        public ICommand AuthenticateCommand { get; }
        public ICommand LogoutCommand { get; }
        public ICommand ExitApplicationCommand { get; }
        public ICommand AboutCommand { get; }

        #endregion

        /// <summary>
        /// Constructor
        /// </summary>
        public MainViewModel()
        {
            // Initialize properties
            ApplicationTitle = "Password Manager - Secure Credential Storage";
            IsAuthenticated = false;
            SelectedTabIndex = 0;

            // Initialize commands
            AuthenticateCommand = new SimpleRelayCommand(Authenticate, () => !string.IsNullOrEmpty(MasterPassword));
            LogoutCommand = new SimpleRelayCommand(Logout, () => IsAuthenticated);
            ExitApplicationCommand = new SimpleRelayCommand(ExitApplication);
            AboutCommand = new SimpleRelayCommand(ShowAbout);

            // Check if this is first run
            CheckFirstRun();
        }

        #region Command Implementations

        private void Authenticate()
        {
            ExecuteSafely(() =>
            {
                if (string.IsNullOrWhiteSpace(MasterPassword))
                {
                    SetError("Please enter your master password.");
                    return;
                }

                // For first run, set up the master password
                if (IsFirstRun())
                {
                    SetupMasterPassword();
                }
                else
                {
                    ValidateMasterPassword();
                }

            }, "Authentication failed");
        }

        private void Logout()
        {
            // Clear sensitive data
            _accountService?.Dispose();
            _accountService = null;
            SaveAccountViewModel?.Dispose();
            SaveAccountViewModel = null;

            // Reset authentication state
            IsAuthenticated = false;
            MasterPassword = string.Empty;
            SelectedTabIndex = 0;

            // Check if master password should be remembered for next login (Windows only)
            if (OperatingSystem.IsWindows() && DatabaseService.IsMasterPasswordRemembered())
            {
                try
                {
                    var rememberedPassword = DatabaseService.GetRememberedMasterPassword();
                    if (!string.IsNullOrEmpty(rememberedPassword))
                    {
                        MasterPassword = rememberedPassword;
                        RememberMasterPassword = true;
                    }
                }
                catch
                {
                    // If there's an error loading remembered password, just continue
                    RememberMasterPassword = false;
                }
            }
            else
            {
                RememberMasterPassword = false;
            }

            SetSuccess("Logged out successfully.");
        }

        private void ExitApplication()
        {
            // Clean up resources
            Logout();

            // Close application
            System.Windows.Application.Current.Shutdown();
        }

        private void ShowAbout()
        {
            var aboutMessage = $@"Password Manager v1.0

A secure, portable password manager for Windows.

Features:
• AES-256 encryption
• Local data storage
• Portable executable
• No internet connection required

Created with WPF and .NET Framework 4.8

Database Location: {DatabaseService.GetDatabasePath()}";

            System.Windows.MessageBox.Show(aboutMessage, "About Password Manager",
                System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }

        #endregion

        #region Helper Methods

        private void CheckFirstRun()
        {
            // Check if database exists and has master password setup
            if (!DatabaseService.DatabaseExists())
            {
                SetSuccess("Welcome! Please set up your master password to get started.");
            }
            else
            {
                // Check if master password is remembered (Windows only)
                if (OperatingSystem.IsWindows() && DatabaseService.IsMasterPasswordRemembered())
                {
                    try
                    {
                        var rememberedPassword = DatabaseService.GetRememberedMasterPassword();
                        if (!string.IsNullOrEmpty(rememberedPassword))
                        {
                            MasterPassword = rememberedPassword;
                            RememberMasterPassword = true;
                            SetSuccess("Master password loaded. Click Unlock to continue.");
                        }
                    }
                    catch (Exception ex)
                    {
                        SetError($"Failed to load remembered password: {ex.Message}");
                    }
                }
            }
        }

        private bool IsFirstRun()
        {
            // Check if database exists and master password is set up
            return !DatabaseService.DatabaseExists() || !DatabaseService.IsMasterPasswordSetup();
        }

        private void SetupMasterPassword()
        {
            try
            {
                // Hash the master password for storage
                var (hash, salt) = EncryptionService.HashMasterPassword(MasterPassword);

                // Store master password hash in database
                DatabaseService.StoreMasterPasswordHash(hash, salt);

                // Store master password if remember option is checked (Windows only)
                if (RememberMasterPassword && OperatingSystem.IsWindows())
                {
                    DatabaseService.StoreRememberedMasterPassword(MasterPassword);
                }

                // Initialize services
                InitializeServices();

                SetSuccess("Master password set up successfully! You can now start saving your accounts.");
            }
            catch (Exception ex)
            {
                SetError($"Failed to set up master password: {ex.Message}");
            }
        }

        private void ValidateMasterPassword()
        {
            try
            {
                // Get stored master password hash and salt
                var (storedHash, storedSalt) = DatabaseService.GetMasterPasswordHash();

                if (string.IsNullOrEmpty(storedHash) || string.IsNullOrEmpty(storedSalt))
                {
                    SetError("Master password not found. Please set up your master password first.");
                    return;
                }

                // Verify the entered password against stored hash
                bool isValid = EncryptionService.VerifyMasterPassword(MasterPassword, storedHash, storedSalt);

                if (!isValid)
                {
                    SetError("Invalid master password. Please try again.");
                    return;
                }

                // Handle remember master password option (Windows only)
                if (OperatingSystem.IsWindows())
                {
                    if (RememberMasterPassword)
                    {
                        DatabaseService.StoreRememberedMasterPassword(MasterPassword);
                    }
                    else
                    {
                        // Clear remembered password if user unchecked the option
                        DatabaseService.ClearRememberedMasterPassword();
                    }
                }

                // Initialize services
                InitializeServices();

                SetSuccess("Authentication successful!");
            }
            catch (Exception ex)
            {
                SetError($"Authentication failed: {ex.Message}");
            }
        }

        private void InitializeServices()
        {
            // Initialize account service with master password
            _accountService = new AccountService(MasterPassword);

            // Initialize ViewModels
            SaveAccountViewModel = new SaveAccountViewModel(_accountService);

            // Set authenticated state
            IsAuthenticated = true;

            // Clear master password from memory for security
            MasterPassword = string.Empty;
        }

        #endregion

        public override void Dispose()
        {
            _accountService?.Dispose();
            SaveAccountViewModel?.Dispose();
            base.Dispose();
        }
    }
}
