using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using Microsoft.Data.Sqlite;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using PasswordManager.Models;

namespace PasswordManager.Services
{
    /// <summary>
    /// Handles all database operations for the password manager with optimized performance
    /// </summary>
    public static class DatabaseService
    {
        private static readonly string DatabasePath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, "passwords.db");

        private static readonly string ConnectionString =
            $"Data Source={DatabasePath};Cache=Shared;Pooling=true;";

        // Connection pool for better performance
        private static readonly ConcurrentQueue<SqliteConnection> _connectionPool = new();
        private static readonly SemaphoreSlim _connectionSemaphore = new(10, 10); // Max 10 connections
        private static volatile bool _isInitialized = false;
        private static readonly object _initLock = new();

        /// <summary>
        /// Initializes the database and creates tables if they don't exist
        /// </summary>
        public static void InitializeDatabase()
        {
            if (_isInitialized) return;

            lock (_initLock)
            {
                if (_isInitialized) return;

                using var tracker = PerformanceMonitor.StartTracking("Database.Initialize");

                try
                {
                    LoggingService.LogInfo("Initializing database...");

                    using (var connection = CreateConnection())
                    {
                        connection.Open();

                        // Enable WAL mode for better performance
                        using (var pragmaCommand = new SqliteCommand("PRAGMA journal_mode=WAL", connection))
                        {
                            pragmaCommand.ExecuteNonQuery();
                        }

                        // Enable foreign keys
                        using (var pragmaCommand = new SqliteCommand("PRAGMA foreign_keys=ON", connection))
                        {
                            pragmaCommand.ExecuteNonQuery();
                        }

                        // Optimize SQLite settings
                        using (var pragmaCommand = new SqliteCommand("PRAGMA synchronous=NORMAL", connection))
                        {
                            pragmaCommand.ExecuteNonQuery();
                        }

                        using (var pragmaCommand = new SqliteCommand("PRAGMA cache_size=10000", connection))
                        {
                            pragmaCommand.ExecuteNonQuery();
                        }

                        using (var pragmaCommand = new SqliteCommand("PRAGMA temp_store=MEMORY", connection))
                        {
                            pragmaCommand.ExecuteNonQuery();
                        }

                        CreateAccountsTable(connection);
                        CreateSettingsTable(connection);
                        CreatePerformanceIndexes(connection);
                    }

                    _isInitialized = true;
                    LoggingService.LogInfo("Database initialized successfully");
                }
                catch (Exception ex)
                {
                    LoggingService.LogError("Failed to initialize database", ex);
                    throw new Exception($"Failed to initialize database: {ex.Message}", ex);
                }
            }
        }

        /// <summary>
        /// Creates a new database connection with optimized settings
        /// </summary>
        private static SqliteConnection CreateConnection()
        {
            var connection = new SqliteConnection(ConnectionString);
            return connection;
        }

        /// <summary>
        /// Gets a connection from the pool or creates a new one
        /// </summary>
        private static async Task<SqliteConnection> GetConnectionAsync()
        {
            await _connectionSemaphore.WaitAsync();

            if (_connectionPool.TryDequeue(out var connection) && connection.State == System.Data.ConnectionState.Open)
            {
                return connection;
            }

            connection = CreateConnection();
            await connection.OpenAsync();
            return connection;
        }

        /// <summary>
        /// Returns a connection to the pool
        /// </summary>
        private static void ReturnConnection(SqliteConnection connection)
        {
            if (connection?.State == System.Data.ConnectionState.Open)
            {
                _connectionPool.Enqueue(connection);
            }
            else
            {
                connection?.Dispose();
            }

            _connectionSemaphore.Release();
        }

        /// <summary>
        /// Creates the accounts table if it doesn't exist
        /// </summary>
        private static void CreateAccountsTable(SqliteConnection connection)
        {
            try
            {
                string createTableQuery = @"
                    CREATE TABLE IF NOT EXISTS Accounts (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        WebsiteName TEXT NOT NULL,
                        WebsiteUrl TEXT,
                        Username TEXT NOT NULL,
                        EncryptedPassword TEXT NOT NULL,
                        Salt TEXT NOT NULL,
                        CreatedDate TEXT NOT NULL,
                        LastModified TEXT NOT NULL,
                        Notes TEXT
                    )";

                using (var command = new SqliteCommand(createTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                LoggingService.LogInfo("Accounts table created successfully");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to create Accounts table", ex);
                throw new Exception($"Failed to create Accounts table: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates performance indexes for optimized queries
        /// </summary>
        private static void CreatePerformanceIndexes(SqliteConnection connection)
        {
            try
            {
                var indexes = new[]
                {
                    "CREATE INDEX IF NOT EXISTS idx_accounts_website_username ON Accounts(WebsiteName, Username)",
                    "CREATE INDEX IF NOT EXISTS idx_accounts_website ON Accounts(WebsiteName)",
                    "CREATE INDEX IF NOT EXISTS idx_accounts_username ON Accounts(Username)",
                    "CREATE INDEX IF NOT EXISTS idx_accounts_lastmodified ON Accounts(LastModified)",
                    "CREATE INDEX IF NOT EXISTS idx_settings_key ON Settings(Key)"
                };

                foreach (var indexQuery in indexes)
                {
                    try
                    {
                        using (var command = new SqliteCommand(indexQuery, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.LogError($"Failed to create index: {indexQuery}", ex);
                        // Continue with other indexes even if one fails
                    }
                }

                LoggingService.LogInfo("Performance indexes created successfully");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to create performance indexes", ex);
                // Don't throw here as indexes are optional for basic functionality
            }
        }

        /// <summary>
        /// Creates the settings table if it doesn't exist
        /// </summary>
        private static void CreateSettingsTable(SqliteConnection connection)
        {
            try
            {
                string createTableQuery = @"
                    CREATE TABLE IF NOT EXISTS Settings (
                        Key TEXT PRIMARY KEY,
                        Value TEXT NOT NULL,
                        CreatedDate TEXT NOT NULL,
                        LastModified TEXT NOT NULL
                    )";

                using (var command = new SqliteCommand(createTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                LoggingService.LogInfo("Settings table created successfully");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to create Settings table", ex);
                throw new Exception($"Failed to create Settings table: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Inserts a new account into the database with optimized performance
        /// </summary>
        public static int InsertAccount(Account account)
        {
            return InsertAccountAsync(account).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Inserts a new account into the database asynchronously with optimized performance
        /// </summary>
        public static async Task<int> InsertAccountAsync(Account account)
        {
            using var tracker = PerformanceMonitor.StartTracking("Database.InsertAccount");

            try
            {
                LoggingService.LogInfo($"Inserting account for website: {account.WebsiteName}");

                var connection = await GetConnectionAsync();
                try
                {
                    string insertQuery = @"
                        INSERT INTO Accounts
                        (WebsiteName, WebsiteUrl, Username, EncryptedPassword, Salt, CreatedDate, LastModified, Notes)
                        VALUES
                        (@WebsiteName, @WebsiteUrl, @Username, @EncryptedPassword, @Salt, @CreatedDate, @LastModified, @Notes);
                        SELECT last_insert_rowid();";

                    using (var command = new SqliteCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@WebsiteName", account.WebsiteName);
                        command.Parameters.AddWithValue("@WebsiteUrl", account.WebsiteUrl ?? string.Empty);
                        command.Parameters.AddWithValue("@Username", account.Username);
                        command.Parameters.AddWithValue("@EncryptedPassword", account.EncryptedPassword);
                        command.Parameters.AddWithValue("@Salt", account.Salt);
                        command.Parameters.AddWithValue("@CreatedDate", account.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.Parameters.AddWithValue("@LastModified", account.LastModified.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.Parameters.AddWithValue("@Notes", account.Notes ?? string.Empty);

                        var result = await command.ExecuteScalarAsync();
                        var accountId = Convert.ToInt32(result);

                        LoggingService.LogInfo($"Account inserted successfully with ID: {accountId}");
                        return accountId;
                    }
                }
                finally
                {
                    ReturnConnection(connection);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to insert account", ex);
                throw new Exception($"Failed to insert account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Updates an existing account in the database with optimized performance
        /// </summary>
        public static void UpdateAccount(Account account)
        {
            UpdateAccountAsync(account).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Updates an existing account in the database asynchronously with optimized performance
        /// </summary>
        public static async Task UpdateAccountAsync(Account account)
        {
            using var tracker = PerformanceMonitor.StartTracking("Database.UpdateAccount");

            try
            {
                LoggingService.LogInfo($"Updating account ID: {account.Id} for website: {account.WebsiteName}");

                var connection = await GetConnectionAsync();
                try
                {
                    string updateQuery = @"
                        UPDATE Accounts SET
                            WebsiteName = @WebsiteName,
                            WebsiteUrl = @WebsiteUrl,
                            Username = @Username,
                            EncryptedPassword = @EncryptedPassword,
                            Salt = @Salt,
                            LastModified = @LastModified,
                            Notes = @Notes
                        WHERE Id = @Id";

                    using (var command = new SqliteCommand(updateQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Id", account.Id);
                        command.Parameters.AddWithValue("@WebsiteName", account.WebsiteName);
                        command.Parameters.AddWithValue("@WebsiteUrl", account.WebsiteUrl ?? string.Empty);
                        command.Parameters.AddWithValue("@Username", account.Username);
                        command.Parameters.AddWithValue("@EncryptedPassword", account.EncryptedPassword);
                        command.Parameters.AddWithValue("@Salt", account.Salt);
                        command.Parameters.AddWithValue("@LastModified", account.LastModified.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.Parameters.AddWithValue("@Notes", account.Notes ?? string.Empty);

                        var rowsAffected = await command.ExecuteNonQueryAsync();
                        LoggingService.LogInfo($"Account updated successfully. Rows affected: {rowsAffected}");
                    }
                }
                finally
                {
                    ReturnConnection(connection);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to update account", ex);
                throw new Exception($"Failed to update account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Deletes an account from the database
        /// </summary>
        public static void DeleteAccount(int accountId)
        {
            try
            {
                using (var connection = new SqliteConnection(ConnectionString))
                {
                    connection.Open();

                    string deleteQuery = "DELETE FROM Accounts WHERE Id = @Id";

                    using (var command = new SqliteCommand(deleteQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Id", accountId);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to delete account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves all accounts from the database with optimized performance
        /// </summary>
        public static List<Account> GetAllAccounts()
        {
            return GetAllAccountsAsync().GetAwaiter().GetResult();
        }

        /// <summary>
        /// Retrieves all accounts from the database asynchronously with optimized performance
        /// </summary>
        public static async Task<List<Account>> GetAllAccountsAsync()
        {
            var accounts = new List<Account>();

            try
            {
                var connection = await GetConnectionAsync();

                try
                {
                    string selectQuery = @"
                        SELECT Id, WebsiteName, WebsiteUrl, Username, EncryptedPassword, Salt, CreatedDate, LastModified, Notes
                        FROM Accounts
                        ORDER BY WebsiteName, Username";

                    using (var command = new SqliteCommand(selectQuery, connection))
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var account = new Account();
                            account.Id = reader.GetInt32(0);
                            account.WebsiteName = reader.GetString(1);
                            account.WebsiteUrl = reader.IsDBNull(2) ? string.Empty : reader.GetString(2);
                            account.Username = reader.GetString(3);
                            account.EncryptedPassword = reader.GetString(4);
                            account.Salt = reader.GetString(5);
                            account.CreatedDate = DateTime.Parse(reader.GetString(6));
                            account.LastModified = DateTime.Parse(reader.GetString(7));
                            account.Notes = reader.IsDBNull(8) ? string.Empty : reader.GetString(8);

                            accounts.Add(account);
                        }
                    }
                }
                finally
                {
                    ReturnConnection(connection);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve accounts: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// Retrieves a specific account by ID
        /// </summary>
        public static Account GetAccountById(int accountId)
        {
            try
            {
                using (var connection = new SqliteConnection(ConnectionString))
                {
                    connection.Open();

                    string selectQuery = "SELECT * FROM Accounts WHERE Id = @Id";

                    using (var command = new SqliteCommand(selectQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Id", accountId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Account
                                {
                                    Id = reader.GetInt32(0),
                                    WebsiteName = reader.GetString(1),
                                    WebsiteUrl = reader.GetString(2),
                                    Username = reader.GetString(3),
                                    EncryptedPassword = reader.GetString(4),
                                    Salt = reader.GetString(5),
                                    CreatedDate = DateTime.Parse(reader.GetString(6)),
                                    LastModified = DateTime.Parse(reader.GetString(7)),
                                    Notes = reader.GetString(8)
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve account: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// Checks if the database file exists
        /// </summary>
        public static bool DatabaseExists()
        {
            return File.Exists(DatabasePath);
        }

        /// <summary>
        /// Gets the database file path
        /// </summary>
        public static string GetDatabasePath()
        {
            return DatabasePath;
        }

        /// <summary>
        /// Ensures database is initialized before operations
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                InitializeDatabase();
            }
        }

        /// <summary>
        /// Stores the master password hash and salt
        /// </summary>
        public static void StoreMasterPasswordHash(string hash, string salt)
        {
            EnsureInitialized();

            try
            {
                using (var connection = new SqliteConnection(ConnectionString))
                {
                    connection.Open();

                    // Store hash
                    string insertHashQuery = @"
                        INSERT OR REPLACE INTO Settings (Key, Value, CreatedDate, LastModified)
                        VALUES (@Key, @Value, @CreatedDate, @LastModified)";

                    using (var command = new SqliteCommand(insertHashQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Key", "MasterPasswordHash");
                        command.Parameters.AddWithValue("@Value", hash);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.Parameters.AddWithValue("@LastModified", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.ExecuteNonQuery();
                    }

                    // Store salt
                    using (var command = new SqliteCommand(insertHashQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Key", "MasterPasswordSalt");
                        command.Parameters.AddWithValue("@Value", salt);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.Parameters.AddWithValue("@LastModified", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to store master password hash: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves the master password hash and salt
        /// </summary>
        public static (string hash, string salt) GetMasterPasswordHash()
        {
            EnsureInitialized();

            try
            {
                using (var connection = new SqliteConnection(ConnectionString))
                {
                    connection.Open();

                    string selectQuery = "SELECT Key, Value FROM Settings WHERE Key IN ('MasterPasswordHash', 'MasterPasswordSalt')";

                    using (var command = new SqliteCommand(selectQuery, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        string hash = null;
                        string salt = null;

                        while (reader.Read())
                        {
                            string key = reader.GetString(0);
                            string value = reader.GetString(1);

                            if (key == "MasterPasswordHash")
                                hash = value;
                            else if (key == "MasterPasswordSalt")
                                salt = value;
                        }

                        return (hash, salt);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve master password hash: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Checks if master password is set up
        /// </summary>
        public static bool IsMasterPasswordSetup()
        {
            try
            {
                var (hash, salt) = GetMasterPasswordHash();
                return !string.IsNullOrEmpty(hash) && !string.IsNullOrEmpty(salt);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Stores the remembered master password (encrypted)
        /// </summary>
        [System.Runtime.Versioning.SupportedOSPlatform("windows")]
        public static void StoreRememberedMasterPassword(string masterPassword)
        {
            EnsureInitialized();

            try
            {
                using (var connection = new SqliteConnection(ConnectionString))
                {
                    connection.Open();

                    // Encrypt the master password with a simple key for storage
                    var encryptedPassword = EncryptionService.EncryptForStorage(masterPassword);

                    string insertQuery = @"
                        INSERT OR REPLACE INTO Settings (Key, Value, CreatedDate, LastModified)
                        VALUES (@Key, @Value, @CreatedDate, @LastModified)";

                    using (var command = new SqliteCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@Key", "RememberedMasterPassword");
                        command.Parameters.AddWithValue("@Value", encryptedPassword);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.Parameters.AddWithValue("@LastModified", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to store remembered master password: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves the remembered master password (decrypted)
        /// </summary>
        [System.Runtime.Versioning.SupportedOSPlatform("windows")]
        public static string GetRememberedMasterPassword()
        {
            EnsureInitialized();

            try
            {
                using (var connection = new SqliteConnection(ConnectionString))
                {
                    connection.Open();

                    string selectQuery = "SELECT Value FROM Settings WHERE Key = 'RememberedMasterPassword'";

                    using (var command = new SqliteCommand(selectQuery, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            string encryptedPassword = reader.GetString(0);
                            return EncryptionService.DecryptFromStorage(encryptedPassword);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve remembered master password: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// Clears the remembered master password
        /// </summary>
        public static void ClearRememberedMasterPassword()
        {
            EnsureInitialized();

            try
            {
                using (var connection = new SqliteConnection(ConnectionString))
                {
                    connection.Open();

                    string deleteQuery = "DELETE FROM Settings WHERE Key = 'RememberedMasterPassword'";

                    using (var command = new SqliteCommand(deleteQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to clear remembered master password: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Checks if master password is remembered
        /// </summary>
        public static bool IsMasterPasswordRemembered()
        {
            if (!OperatingSystem.IsWindows())
                return false;

            try
            {
                var rememberedPassword = GetRememberedMasterPassword();
                return !string.IsNullOrEmpty(rememberedPassword);
            }
            catch
            {
                return false;
            }
        }
    }
}
