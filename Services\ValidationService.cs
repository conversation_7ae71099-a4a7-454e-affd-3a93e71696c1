using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

namespace PasswordManager.Services
{
    /// <summary>
    /// Comprehensive validation service for input sanitization and security
    /// </summary>
    public static class ValidationService
    {
        private static readonly Regex EmailRegex = new(@"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", 
            RegexOptions.Compiled | RegexOptions.IgnoreCase);
        
        private static readonly Regex UrlRegex = new(@"^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$", 
            RegexOptions.Compiled | RegexOptions.IgnoreCase);
        
        private static readonly Regex WebsiteNameRegex = new(@"^[a-zA-Z0-9\s\-_.()&]+$", 
            RegexOptions.Compiled);

        private static readonly HashSet<string> CommonPasswords = new(StringComparer.OrdinalIgnoreCase)
        {
            "password", "123456", "password123", "admin", "qwerty", "letmein", "welcome",
            "monkey", "1234567890", "abc123", "111111", "123123", "password1", "1234",
            "12345", "dragon", "master", "login", "princess", "solo", "hello", "sunshine"
        };

        /// <summary>
        /// Validation result containing success status and error messages
        /// </summary>
        public class ValidationResult
        {
            public bool IsValid { get; set; }
            public List<string> Errors { get; set; } = new();
            public List<string> Warnings { get; set; } = new();

            public string GetErrorMessage() => string.Join("; ", Errors);
            public string GetWarningMessage() => string.Join("; ", Warnings);
        }

        /// <summary>
        /// Validates website name input
        /// </summary>
        public static ValidationResult ValidateWebsiteName(string websiteName)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(websiteName))
            {
                result.Errors.Add("Website name is required");
                return result;
            }

            websiteName = websiteName.Trim();

            if (websiteName.Length < 2)
            {
                result.Errors.Add("Website name must be at least 2 characters long");
            }

            if (websiteName.Length > 100)
            {
                result.Errors.Add("Website name cannot exceed 100 characters");
            }

            if (!WebsiteNameRegex.IsMatch(websiteName))
            {
                result.Errors.Add("Website name contains invalid characters");
            }

            // Check for potential security issues
            if (ContainsSqlInjectionPatterns(websiteName))
            {
                result.Errors.Add("Website name contains potentially dangerous characters");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Validates website URL input
        /// </summary>
        public static ValidationResult ValidateWebsiteUrl(string websiteUrl)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(websiteUrl))
            {
                // URL is optional
                result.IsValid = true;
                return result;
            }

            websiteUrl = websiteUrl.Trim();

            if (websiteUrl.Length > 500)
            {
                result.Errors.Add("Website URL cannot exceed 500 characters");
            }

            if (!UrlRegex.IsMatch(websiteUrl))
            {
                result.Errors.Add("Website URL format is invalid");
            }

            // Security check
            if (ContainsSqlInjectionPatterns(websiteUrl))
            {
                result.Errors.Add("Website URL contains potentially dangerous characters");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Validates username input
        /// </summary>
        public static ValidationResult ValidateUsername(string username)
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(username))
            {
                result.Errors.Add("Username is required");
                return result;
            }

            username = username.Trim();

            if (username.Length < 1)
            {
                result.Errors.Add("Username cannot be empty");
            }

            if (username.Length > 200)
            {
                result.Errors.Add("Username cannot exceed 200 characters");
            }

            // Check for potential security issues
            if (ContainsSqlInjectionPatterns(username))
            {
                result.Errors.Add("Username contains potentially dangerous characters");
            }

            // Check if it looks like an email
            if (EmailRegex.IsMatch(username))
            {
                result.Warnings.Add("Username appears to be an email address");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Validates password strength and security
        /// </summary>
        public static ValidationResult ValidatePassword(string password)
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(password))
            {
                result.Errors.Add("Password is required");
                return result;
            }

            if (password.Length < 8)
            {
                result.Errors.Add("Password must be at least 8 characters long");
            }

            if (password.Length > 128)
            {
                result.Errors.Add("Password cannot exceed 128 characters");
            }

            // Check for common weak passwords
            if (CommonPasswords.Contains(password))
            {
                result.Errors.Add("Password is too common and easily guessable");
            }

            // Strength checks
            bool hasUpper = password.Any(char.IsUpper);
            bool hasLower = password.Any(char.IsLower);
            bool hasDigit = password.Any(char.IsDigit);
            bool hasSpecial = password.Any(c => !char.IsLetterOrDigit(c));

            int strengthScore = 0;
            if (hasUpper) strengthScore++;
            if (hasLower) strengthScore++;
            if (hasDigit) strengthScore++;
            if (hasSpecial) strengthScore++;

            if (strengthScore < 3)
            {
                result.Warnings.Add("Password should contain uppercase, lowercase, numbers, and special characters");
            }

            // Check for repeated characters
            if (HasRepeatedCharacters(password))
            {
                result.Warnings.Add("Password contains repeated character patterns");
            }

            // Check for sequential characters
            if (HasSequentialCharacters(password))
            {
                result.Warnings.Add("Password contains sequential character patterns");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Validates notes input
        /// </summary>
        public static ValidationResult ValidateNotes(string notes)
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(notes))
            {
                // Notes are optional
                result.IsValid = true;
                return result;
            }

            if (notes.Length > 1000)
            {
                result.Errors.Add("Notes cannot exceed 1000 characters");
            }

            // Check for potential security issues
            if (ContainsSqlInjectionPatterns(notes))
            {
                result.Errors.Add("Notes contain potentially dangerous characters");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Validates master password
        /// </summary>
        public static ValidationResult ValidateMasterPassword(string masterPassword)
        {
            var result = new ValidationResult();

            if (string.IsNullOrEmpty(masterPassword))
            {
                result.Errors.Add("Master password is required");
                return result;
            }

            if (masterPassword.Length < 12)
            {
                result.Errors.Add("Master password must be at least 12 characters long");
            }

            if (masterPassword.Length > 256)
            {
                result.Errors.Add("Master password cannot exceed 256 characters");
            }

            // Stronger requirements for master password
            bool hasUpper = masterPassword.Any(char.IsUpper);
            bool hasLower = masterPassword.Any(char.IsLower);
            bool hasDigit = masterPassword.Any(char.IsDigit);
            bool hasSpecial = masterPassword.Any(c => !char.IsLetterOrDigit(c));

            if (!hasUpper || !hasLower || !hasDigit || !hasSpecial)
            {
                result.Errors.Add("Master password must contain uppercase, lowercase, numbers, and special characters");
            }

            // Check for common weak passwords
            if (CommonPasswords.Contains(masterPassword))
            {
                result.Errors.Add("Master password is too common and easily guessable");
            }

            result.IsValid = result.Errors.Count == 0;
            return result;
        }

        /// <summary>
        /// Checks for SQL injection patterns
        /// </summary>
        private static bool ContainsSqlInjectionPatterns(string input)
        {
            if (string.IsNullOrEmpty(input)) return false;

            var dangerousPatterns = new[]
            {
                "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_", "exec", "execute",
                "select", "insert", "update", "delete", "drop", "create", "alter",
                "union", "script", "javascript:", "vbscript:", "onload", "onerror"
            };

            var lowerInput = input.ToLowerInvariant();
            return dangerousPatterns.Any(pattern => lowerInput.Contains(pattern));
        }

        /// <summary>
        /// Checks for repeated character patterns
        /// </summary>
        private static bool HasRepeatedCharacters(string password)
        {
            if (password.Length < 3) return false;

            for (int i = 0; i <= password.Length - 3; i++)
            {
                if (password[i] == password[i + 1] && password[i + 1] == password[i + 2])
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks for sequential character patterns
        /// </summary>
        private static bool HasSequentialCharacters(string password)
        {
            if (password.Length < 3) return false;

            for (int i = 0; i <= password.Length - 3; i++)
            {
                char c1 = password[i];
                char c2 = password[i + 1];
                char c3 = password[i + 2];

                if ((c2 == c1 + 1 && c3 == c2 + 1) || (c2 == c1 - 1 && c3 == c2 - 1))
                {
                    return true;
                }
            }

            return false;
        }
    }
}
