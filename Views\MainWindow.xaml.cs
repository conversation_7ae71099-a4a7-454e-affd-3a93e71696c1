using System.Windows;
using System.Windows.Controls;
using PasswordManager.ViewModels;

namespace PasswordManager.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private MainViewModel _viewModel;

        public MainWindow()
        {
            InitializeComponent();
            _viewModel = DataContext as MainViewModel;
        }

        /// <summary>
        /// Handles password changes in the master password box
        /// </summary>
        private void MasterPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox && _viewModel != null)
            {
                _viewModel.MasterPassword = passwordBox.Password;
            }
        }

        /// <summary>
        /// Handles password changes in the account password input
        /// </summary>
        private void PasswordInput_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (sender is PasswordBox passwordBox && _viewModel?.SaveAccountViewModel != null)
            {
                _viewModel.SaveAccountViewModel.Password = passwordBox.Password;
            }
        }

        /// <summary>
        /// Handle window closing to ensure proper cleanup
        /// </summary>
        protected override void OnClosed(System.EventArgs e)
        {
            _viewModel?.Dispose();
            base.OnClosed(e);
        }

        /// <summary>
        /// Handle window loaded event
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Focus on master password box when window loads
            if (!_viewModel?.IsAuthenticated == true)
            {
                MasterPasswordBox?.Focus();
            }

            // Subscribe to password update events
            if (_viewModel?.SaveAccountViewModel != null)
            {
                _viewModel.SaveAccountViewModel.OnPasswordUpdated += UpdatePasswordBox;
            }
        }

        /// <summary>
        /// Updates the PasswordBox when password is set programmatically
        /// </summary>
        private void UpdatePasswordBox(string newPassword)
        {
            if (PasswordInput != null)
            {
                PasswordInput.Password = newPassword;
            }
        }
    }
}
