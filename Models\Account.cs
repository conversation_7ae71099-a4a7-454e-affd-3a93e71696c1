using System;
using System.ComponentModel;

namespace PasswordManager.Models
{
    /// <summary>
    /// Represents an account with website credentials
    /// </summary>
    public class Account : INotifyPropertyChanged
    {
        private int _id;
        private string _websiteName;
        private string _websiteUrl;
        private string _username;
        private string _encryptedPassword;
        private string _salt;
        private DateTime _createdDate;
        private DateTime _lastModified;
        private string _notes;

        /// <summary>
        /// Unique identifier for the account
        /// </summary>
        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        /// <summary>
        /// Display name for the website (e.g., "Google", "Facebook")
        /// </summary>
        public string WebsiteName
        {
            get => _websiteName;
            set
            {
                _websiteName = value;
                OnPropertyChanged(nameof(WebsiteName));
            }
        }

        /// <summary>
        /// Full URL of the website
        /// </summary>
        public string WebsiteUrl
        {
            get => _websiteUrl;
            set
            {
                _websiteUrl = value;
                OnPropertyChanged(nameof(WebsiteUrl));
            }
        }

        /// <summary>
        /// Username or email for the account
        /// </summary>
        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged(nameof(Username));
            }
        }

        /// <summary>
        /// Encrypted password (never store plain text)
        /// </summary>
        public string EncryptedPassword
        {
            get => _encryptedPassword;
            set
            {
                _encryptedPassword = value;
                OnPropertyChanged(nameof(EncryptedPassword));
            }
        }

        /// <summary>
        /// Salt used for password encryption
        /// </summary>
        public string Salt
        {
            get => _salt;
            set
            {
                _salt = value;
                OnPropertyChanged(nameof(Salt));
            }
        }

        /// <summary>
        /// When the account was created
        /// </summary>
        public DateTime CreatedDate
        {
            get => _createdDate;
            set
            {
                _createdDate = value;
                OnPropertyChanged(nameof(CreatedDate));
            }
        }

        /// <summary>
        /// When the account was last modified
        /// </summary>
        public DateTime LastModified
        {
            get => _lastModified;
            set
            {
                _lastModified = value;
                OnPropertyChanged(nameof(LastModified));
            }
        }

        /// <summary>
        /// Optional notes about the account
        /// </summary>
        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged(nameof(Notes));
            }
        }

        /// <summary>
        /// Constructor for new account
        /// </summary>
        public Account()
        {
            CreatedDate = DateTime.Now;
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// Constructor with basic information
        /// </summary>
        public Account(string websiteName, string websiteUrl, string username) : this()
        {
            WebsiteName = websiteName;
            WebsiteUrl = websiteUrl;
            Username = username;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Updates the LastModified timestamp
        /// </summary>
        public void UpdateModifiedDate()
        {
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// Returns a string representation of the account
        /// </summary>
        public override string ToString()
        {
            return $"{WebsiteName} - {Username}";
        }
    }
}
