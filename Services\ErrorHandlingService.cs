using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;

namespace PasswordManager.Services
{
    /// <summary>
    /// Centralized error handling service with user-friendly messages and recovery strategies
    /// </summary>
    public static class ErrorHandlingService
    {
        private static readonly Dictionary<Type, string> ErrorMessages = new()
        {
            { typeof(UnauthorizedAccessException), "Access denied. Please check your permissions and try again." },
            { typeof(System.IO.FileNotFoundException), "Required file not found. The application may need to be reinstalled." },
            { typeof(System.IO.DirectoryNotFoundException), "Required directory not found. The application will attempt to create it." },

            { typeof(Microsoft.Data.Sqlite.SqliteException), "Database error occurred. Please try again or contact support." },
            { typeof(System.Security.Cryptography.CryptographicException), "Encryption error occurred. Your data may be corrupted." },
            { typeof(OutOfMemoryException), "The application is running low on memory. Please close other applications and try again." },
            { typeof(TimeoutException), "The operation timed out. Please check your connection and try again." },
            { typeof(ArgumentException), "Invalid input provided. Please check your data and try again." },
            { typeof(InvalidOperationException), "Invalid operation. Please try again or restart the application." }
        };

        private static readonly Dictionary<Type, string> ArabicErrorMessages = new()
        {
            { typeof(UnauthorizedAccessException), "تم رفض الوصول. يرجى التحقق من الصلاحيات والمحاولة مرة أخرى." },
            { typeof(System.IO.FileNotFoundException), "الملف المطلوب غير موجود. قد تحتاج إلى إعادة تثبيت التطبيق." },
            { typeof(System.IO.DirectoryNotFoundException), "المجلد المطلوب غير موجود. سيحاول التطبيق إنشاؤه." },

            { typeof(Microsoft.Data.Sqlite.SqliteException), "حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى أو الاتصال بالدعم." },
            { typeof(System.Security.Cryptography.CryptographicException), "حدث خطأ في التشفير. قد تكون بياناتك تالفة." },
            { typeof(OutOfMemoryException), "التطبيق يعاني من نقص في الذاكرة. يرجى إغلاق التطبيقات الأخرى والمحاولة مرة أخرى." },
            { typeof(TimeoutException), "انتهت مهلة العملية. يرجى التحقق من الاتصال والمحاولة مرة أخرى." },
            { typeof(ArgumentException), "تم تقديم مدخلات غير صحيحة. يرجى التحقق من البيانات والمحاولة مرة أخرى." },
            { typeof(InvalidOperationException), "عملية غير صحيحة. يرجى المحاولة مرة أخرى أو إعادة تشغيل التطبيق." }
        };

        /// <summary>
        /// Error severity levels
        /// </summary>
        public enum ErrorSeverity
        {
            Low,        // Minor issues that don't affect functionality
            Medium,     // Issues that affect some functionality
            High,       // Issues that significantly impact functionality
            Critical    // Issues that make the application unusable
        }

        /// <summary>
        /// Error information with recovery suggestions
        /// </summary>
        public class ErrorInfo
        {
            public string UserMessage { get; set; }
            public string ArabicMessage { get; set; }
            public string TechnicalDetails { get; set; }
            public ErrorSeverity Severity { get; set; }
            public List<string> RecoveryActions { get; set; } = new();
            public bool CanRetry { get; set; }
            public bool ShouldRestart { get; set; }
            public bool ShouldExit { get; set; }
        }

        /// <summary>
        /// Handles an exception and returns user-friendly error information
        /// </summary>
        public static ErrorInfo HandleException(Exception exception, string context = null, bool useArabic = false)
        {
            if (exception == null) return null;

            LoggingService.LogError($"Exception in {context ?? "Unknown context"}", exception);

            var errorInfo = new ErrorInfo
            {
                TechnicalDetails = $"{exception.GetType().Name}: {exception.Message}",
                UserMessage = GetUserFriendlyMessage(exception),
                ArabicMessage = GetArabicMessage(exception),
                Severity = DetermineSeverity(exception),
                CanRetry = CanRetryOperation(exception),
                ShouldRestart = ShouldRestartApplication(exception),
                ShouldExit = ShouldExitApplication(exception)
            };

            errorInfo.RecoveryActions = GetRecoveryActions(exception);

            // Log security-related errors
            if (IsSecurityRelated(exception))
            {
                LoggingService.LogSecurity($"Security-related exception: {exception.GetType().Name}",
                    $"Context: {context}, Message: {exception.Message}");
            }

            return errorInfo;
        }

        /// <summary>
        /// Executes an action with comprehensive error handling
        /// </summary>
        public static async Task<T> ExecuteWithErrorHandlingAsync<T>(
            Func<Task<T>> action,
            string operationName,
            T defaultValue = default(T),
            int maxRetries = 3)
        {
            Exception lastException = null;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    using var tracker = PerformanceMonitor.StartTracking(operationName);
                    return await action();
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    var errorInfo = HandleException(ex, operationName);

                    if (!errorInfo.CanRetry || attempt == maxRetries)
                    {
                        LoggingService.LogError($"Operation '{operationName}' failed after {attempt} attempts", ex);
                        break;
                    }

                    LoggingService.LogWarning($"Operation '{operationName}' failed on attempt {attempt}, retrying...", ex);

                    // Wait before retry with exponential backoff
                    await Task.Delay(TimeSpan.FromMilliseconds(Math.Pow(2, attempt) * 100));
                }
            }

            // If we get here, all retries failed
            var finalErrorInfo = HandleException(lastException, operationName);

            if (finalErrorInfo.ShouldExit)
            {
                LoggingService.LogCritical($"Critical error in '{operationName}', application will exit", lastException);
                Application.Current?.Shutdown();
            }
            else if (finalErrorInfo.ShouldRestart)
            {
                LoggingService.LogError($"Severe error in '{operationName}', application restart recommended", lastException);
                // Could implement restart logic here
            }

            return defaultValue;
        }

        /// <summary>
        /// Gets a user-friendly error message
        /// </summary>
        private static string GetUserFriendlyMessage(Exception exception)
        {
            var exceptionType = exception.GetType();

            if (ErrorMessages.TryGetValue(exceptionType, out var message))
            {
                return message;
            }

            // Check base types
            var baseType = exceptionType.BaseType;
            while (baseType != null && baseType != typeof(object))
            {
                if (ErrorMessages.TryGetValue(baseType, out message))
                {
                    return message;
                }
                baseType = baseType.BaseType;
            }

            return "An unexpected error occurred. Please try again or contact support if the problem persists.";
        }

        /// <summary>
        /// Gets an Arabic error message
        /// </summary>
        private static string GetArabicMessage(Exception exception)
        {
            var exceptionType = exception.GetType();

            if (ArabicErrorMessages.TryGetValue(exceptionType, out var message))
            {
                return message;
            }

            return "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم إذا استمرت المشكلة.";
        }

        /// <summary>
        /// Determines the severity of an exception
        /// </summary>
        private static ErrorSeverity DetermineSeverity(Exception exception)
        {
            return exception switch
            {
                OutOfMemoryException => ErrorSeverity.Critical,
                System.Security.Cryptography.CryptographicException => ErrorSeverity.Critical,
                UnauthorizedAccessException => ErrorSeverity.High,
                System.IO.FileNotFoundException => ErrorSeverity.High,
                Microsoft.Data.Sqlite.SqliteException => ErrorSeverity.High,
                TimeoutException => ErrorSeverity.Medium,
                ArgumentException => ErrorSeverity.Medium,
                InvalidOperationException => ErrorSeverity.Medium,
                _ => ErrorSeverity.Low
            };
        }

        /// <summary>
        /// Determines if an operation can be retried
        /// </summary>
        private static bool CanRetryOperation(Exception exception)
        {
            return exception switch
            {
                OutOfMemoryException => false,
                System.Security.Cryptography.CryptographicException => false,
                UnauthorizedAccessException => false,
                ArgumentException => false,
                TimeoutException => true,
                System.IO.IOException => true,
                Microsoft.Data.Sqlite.SqliteException => true,
                _ => true
            };
        }

        /// <summary>
        /// Determines if the application should restart
        /// </summary>
        private static bool ShouldRestartApplication(Exception exception)
        {
            return exception switch
            {
                OutOfMemoryException => true,
                System.Security.Cryptography.CryptographicException => true,
                _ => false
            };
        }

        /// <summary>
        /// Determines if the application should exit
        /// </summary>
        private static bool ShouldExitApplication(Exception exception)
        {
            return exception switch
            {
                OutOfMemoryException when GC.GetTotalMemory(false) > 1_000_000_000 => true, // > 1GB
                _ => false
            };
        }

        /// <summary>
        /// Gets recovery actions for an exception
        /// </summary>
        private static List<string> GetRecoveryActions(Exception exception)
        {
            return exception switch
            {
                OutOfMemoryException => new List<string>
                {
                    "Close other applications to free memory",
                    "Restart the application",
                    "Restart your computer if the problem persists"
                },
                System.IO.FileNotFoundException => new List<string>
                {
                    "Check if the file exists",
                    "Reinstall the application",
                    "Contact support"
                },
                UnauthorizedAccessException => new List<string>
                {
                    "Run the application as administrator",
                    "Check file permissions",
                    "Contact your system administrator"
                },
                TimeoutException => new List<string>
                {
                    "Check your internet connection",
                    "Try again in a few moments",
                    "Restart the application"
                },
                _ => new List<string>
                {
                    "Try the operation again",
                    "Restart the application if the problem persists",
                    "Contact support if the issue continues"
                }
            };
        }

        /// <summary>
        /// Checks if an exception is security-related
        /// </summary>
        private static bool IsSecurityRelated(Exception exception)
        {
            return exception is System.Security.Cryptography.CryptographicException ||
                   exception is UnauthorizedAccessException ||
                   exception is System.Security.SecurityException;
        }
    }
}
