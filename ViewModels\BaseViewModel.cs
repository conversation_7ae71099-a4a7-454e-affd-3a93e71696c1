using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using PasswordManager.Services;

namespace PasswordManager.ViewModels
{
    /// <summary>
    /// Base class for all ViewModels, providing INotifyPropertyChanged implementation
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged, IDisposable
    {
        /// <summary>
        /// Occurs when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">Name of the property that changed. If not provided, the caller member name is used.</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets the property value and raises PropertyChanged if the value has changed.
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">New value to set</param>
        /// <param name="propertyName">Name of the property. If not provided, the caller member name is used.</param>
        /// <returns>True if the value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Sets the property value and raises PropertyChanged if the value has changed.
        /// Also executes an action after the property is set.
        /// </summary>
        /// <typeparam name="T">Type of the property</typeparam>
        /// <param name="field">Reference to the backing field</param>
        /// <param name="value">New value to set</param>
        /// <param name="onChanged">Action to execute after the property is changed</param>
        /// <param name="propertyName">Name of the property. If not provided, the caller member name is used.</param>
        /// <returns>True if the value was changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, Action onChanged, [CallerMemberName] string propertyName = null)
        {
            if (SetProperty(ref field, value, propertyName))
            {
                onChanged?.Invoke();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Raises PropertyChanged for multiple properties.
        /// </summary>
        /// <param name="propertyNames">Names of the properties that changed</param>
        protected void OnPropertiesChanged(params string[] propertyNames)
        {
            if (propertyNames == null)
                return;

            foreach (string propertyName in propertyNames)
            {
                OnPropertyChanged(propertyName);
            }
        }

        /// <summary>
        /// Indicates whether the ViewModel is currently busy (e.g., loading data, processing)
        /// </summary>
        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set => SetProperty(ref _isBusy, value);
        }

        private string _loadingMessage;
        public string LoadingMessage
        {
            get => _loadingMessage;
            set => SetProperty(ref _loadingMessage, value);
        }

        private double _progressPercentage;
        public double ProgressPercentage
        {
            get => _progressPercentage;
            set => SetProperty(ref _progressPercentage, value);
        }

        private bool _isProgressVisible;
        public bool IsProgressVisible
        {
            get => _isProgressVisible;
            set => SetProperty(ref _isProgressVisible, value);
        }

        /// <summary>
        /// Error message to display to the user
        /// </summary>
        private string _errorMessage;
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Success message to display to the user
        /// </summary>
        private string _successMessage;
        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        /// <summary>
        /// Indicates whether there is an error
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// Indicates whether there is a success message
        /// </summary>
        public bool HasSuccess => !string.IsNullOrEmpty(SuccessMessage);

        /// <summary>
        /// Clears all messages (error and success)
        /// </summary>
        protected void ClearMessages()
        {
            ErrorMessage = null;
            SuccessMessage = null;
        }

        /// <summary>
        /// Sets an error message and clears any success message
        /// </summary>
        /// <param name="message">The error message to display</param>
        protected void SetError(string message)
        {
            ErrorMessage = message;
            SuccessMessage = null;
            LoggingService.LogError($"Error in {GetType().Name}: {message}");
        }

        /// <summary>
        /// Sets a success message and clears any error message
        /// </summary>
        /// <param name="message">The success message to display</param>
        protected void SetSuccess(string message)
        {
            SuccessMessage = message;
            ErrorMessage = null;
            LoggingService.LogInfo($"Success in {GetType().Name}: {message}");
        }

        /// <summary>
        /// Shows loading state with optional message
        /// </summary>
        protected void ShowLoading(string message = "Loading...")
        {
            IsBusy = true;
            LoadingMessage = message;
            IsProgressVisible = false;
            ProgressPercentage = 0;
        }

        /// <summary>
        /// Shows progress with percentage and message
        /// </summary>
        protected void ShowProgress(double percentage, string message = "Processing...")
        {
            IsBusy = true;
            LoadingMessage = message;
            IsProgressVisible = true;
            ProgressPercentage = Math.Max(0, Math.Min(100, percentage));
        }

        /// <summary>
        /// Hides loading state
        /// </summary>
        protected void HideLoading()
        {
            IsBusy = false;
            LoadingMessage = null;
            IsProgressVisible = false;
            ProgressPercentage = 0;
        }

        /// <summary>
        /// Executes an action safely, handling exceptions and setting error messages
        /// </summary>
        /// <param name="action">The action to execute</param>
        /// <param name="errorPrefix">Prefix for error messages</param>
        protected void ExecuteSafely(Action action, string errorPrefix = "An error occurred")
        {
            try
            {
                ClearMessages();
                IsBusy = true;
                action?.Invoke();
            }
            catch (Exception ex)
            {
                SetError($"{errorPrefix}: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// Executes an action safely with a return value, handling exceptions and setting error messages
        /// </summary>
        /// <typeparam name="T">Return type</typeparam>
        /// <param name="func">The function to execute</param>
        /// <param name="errorPrefix">Prefix for error messages</param>
        /// <param name="defaultValue">Default value to return on error</param>
        /// <returns>The result of the function or the default value on error</returns>
        protected T ExecuteSafely<T>(Func<T> func, string errorPrefix = "An error occurred", T defaultValue = default(T))
        {
            try
            {
                ClearMessages();
                IsBusy = true;
                return func != null ? func() : defaultValue;
            }
            catch (Exception ex)
            {
                SetError($"{errorPrefix}: {ex.Message}");
                return defaultValue;
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// Executes an async action safely with error handling
        /// </summary>
        protected async Task ExecuteSafelyAsync(Func<Task> action, string errorPrefix = "An error occurred")
        {
            try
            {
                ClearMessages();
                IsBusy = true;
                await action();
            }
            catch (Exception ex)
            {
                SetError($"{errorPrefix}: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        /// <summary>
        /// Disposes of the ViewModel resources
        /// </summary>
        public virtual void Dispose()
        {
            // Override in derived classes to dispose of specific resources
        }
    }
}
