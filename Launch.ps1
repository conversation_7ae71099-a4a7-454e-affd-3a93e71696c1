# Quick PowerShell Launcher for Password Manager
# This is the main PowerShell launcher - calls the organized launcher in Launchers folder

param(
    [switch]$DirectLaunch,
    [switch]$SkipBuild,
    [switch]$Help
)

Write-Host ""
Write-Host "Password Manager - Quick PowerShell Launcher" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan
Write-Host ""

if (Test-Path "Launchers\Start-PasswordManager.ps1") {
    if ($Help) {
        & "Launchers\Start-PasswordManager.ps1" -Help
    } elseif ($DirectLaunch) {
        & "Launchers\Start-PasswordManager.ps1" -DirectLaunch
    } elseif ($SkipBuild) {
        & "Launchers\Start-PasswordManager.ps1" -SkipBuild
    } else {
        & "Launchers\Start-PasswordManager.ps1"
    }
} else {
    Write-Host "[ERROR] Advanced launcher not found!" -ForegroundColor Red
    Write-Host "Please ensure Launchers\Start-PasswordManager.ps1 exists." -ForegroundColor Gray
}
