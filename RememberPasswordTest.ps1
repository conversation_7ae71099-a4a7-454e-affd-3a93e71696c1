# Remember Password Functionality Test
Write-Host "Password Manager - Remember Password Functionality Test" -ForegroundColor Cyan
Write-Host "======================================================" -ForegroundColor Cyan
Write-Host ""

$AppDir = ".\bin\Release\net8.0-windows\win-x64"
$DbPath = "$AppDir\passwords.db"
$TestPassword = "123456medoissaA@@"

# Test 1: Check if database exists
Write-Host "Test 1: Database State" -ForegroundColor Yellow
if (Test-Path $DbPath) {
    $dbInfo = Get-Item $DbPath
    Write-Host "[OK] Database exists" -ForegroundColor Green
    Write-Host "    Size: $($dbInfo.Length) bytes" -ForegroundColor Gray
    Write-Host "    Modified: $($dbInfo.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "[ERROR] Database does not exist" -ForegroundColor Red
    Write-Host "    Please run the application first to create the database" -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Test 2: Check Windows DPAPI functionality
Write-Host "Test 2: Windows DPAPI Functionality" -ForegroundColor Yellow
try {
    # Test basic DPAPI encryption/decryption
    $testData = "TestData123"
    $encryptedData = [System.Security.Cryptography.ProtectedData]::Protect(
        [System.Text.Encoding]::UTF8.GetBytes($testData),
        $null,
        [System.Security.Cryptography.DataProtectionScope]::CurrentUser
    )
    $decryptedData = [System.Security.Cryptography.ProtectedData]::Unprotect(
        $encryptedData,
        $null,
        [System.Security.Cryptography.DataProtectionScope]::CurrentUser
    )
    $decryptedString = [System.Text.Encoding]::UTF8.GetString($decryptedData)
    
    if ($decryptedString -eq $testData) {
        Write-Host "[OK] Windows DPAPI is working correctly" -ForegroundColor Green
    } else {
        Write-Host "[ERROR] Windows DPAPI test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "[ERROR] Windows DPAPI is not available: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Check database Settings table
Write-Host "Test 3: Database Settings Table" -ForegroundColor Yellow
try {
    # Load SQLite assembly
    Add-Type -Path "$AppDir\Microsoft.Data.Sqlite.dll" -ErrorAction Stop
    
    $connectionString = "Data Source=$DbPath"
    $connection = New-Object Microsoft.Data.Sqlite.SqliteConnection($connectionString)
    $connection.Open()
    
    # Check if Settings table exists
    $command = $connection.CreateCommand()
    $command.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name='Settings'"
    $result = $command.ExecuteScalar()
    
    if ($result) {
        Write-Host "[OK] Settings table exists" -ForegroundColor Green
        
        # Check for RememberedMasterPassword entry
        $command.CommandText = "SELECT Value FROM Settings WHERE Key = 'RememberedMasterPassword'"
        $rememberedPassword = $command.ExecuteScalar()
        
        if ($rememberedPassword) {
            Write-Host "[INFO] Remembered password entry found" -ForegroundColor Blue
            Write-Host "    Encrypted length: $($rememberedPassword.Length) characters" -ForegroundColor Gray
        } else {
            Write-Host "[INFO] No remembered password entry found" -ForegroundColor Blue
        }
        
        # List all settings
        Write-Host ""
        Write-Host "All Settings entries:" -ForegroundColor Gray
        $command.CommandText = "SELECT Key, Value FROM Settings"
        $reader = $command.ExecuteReader()
        
        while ($reader.Read()) {
            $key = $reader["Key"]
            $value = $reader["Value"]
            
            if ($key -like "*Password*") {
                $valuePreview = $value.Substring(0, [Math]::Min(10, $value.Length)) + "..."
                Write-Host "    $key : $valuePreview (length: $($value.Length))" -ForegroundColor Gray
            } else {
                Write-Host "    $key : $value" -ForegroundColor Gray
            }
        }
        $reader.Close()
        
    } else {
        Write-Host "[ERROR] Settings table does not exist" -ForegroundColor Red
    }
    
    $connection.Close()
    
} catch {
    Write-Host "[ERROR] Database access failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 4: Manual remember password test
Write-Host "Test 4: Manual Remember Password Test" -ForegroundColor Yellow
Write-Host "This test will simulate the remember password functionality" -ForegroundColor Gray
Write-Host ""

$testResult = Read-Host "Have you successfully authenticated with 'Remember master password' checked? (y/n)"

if ($testResult -eq 'y' -or $testResult -eq 'Y') {
    Write-Host ""
    Write-Host "Expected behavior after restart:" -ForegroundColor Yellow
    Write-Host "1. Password field should be pre-filled with your password" -ForegroundColor White
    Write-Host "2. 'Remember master password' checkbox should be checked" -ForegroundColor White
    Write-Host "3. Log should show: 'Master password loaded. Click Unlock to continue.'" -ForegroundColor White
    Write-Host ""
    
    $actualResult = Read-Host "Does the application behave as expected on restart? (y/n)"
    
    if ($actualResult -eq 'y' -or $actualResult -eq 'Y') {
        Write-Host "[SUCCESS] Remember password functionality is working correctly!" -ForegroundColor Green
    } else {
        Write-Host "[ISSUE] Remember password functionality is not working as expected" -ForegroundColor Red
        Write-Host ""
        Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
        Write-Host "1. Check if Windows DPAPI is working (Test 2 above)" -ForegroundColor White
        Write-Host "2. Verify database has RememberedMasterPassword entry (Test 3 above)" -ForegroundColor White
        Write-Host "3. Check application logs for encryption/decryption errors" -ForegroundColor White
        Write-Host "4. Try running application as Administrator" -ForegroundColor White
        Write-Host "5. Check Windows Event Viewer for DPAPI errors" -ForegroundColor White
    }
} else {
    Write-Host ""
    Write-Host "Please follow these steps to test remember password:" -ForegroundColor Yellow
    Write-Host "1. Launch Password Manager" -ForegroundColor White
    Write-Host "2. CHECK the 'Remember master password' checkbox" -ForegroundColor White
    Write-Host "3. Enter your password: $TestPassword" -ForegroundColor White
    Write-Host "4. Click 'Unlock' and verify successful authentication" -ForegroundColor White
    Write-Host "5. Close the application completely" -ForegroundColor White
    Write-Host "6. Restart Password Manager" -ForegroundColor White
    Write-Host "7. Check if password is pre-filled and checkbox is checked" -ForegroundColor White
}

Write-Host ""

# Test 5: Check latest logs
Write-Host "Test 5: Latest Application Logs" -ForegroundColor Yellow
$LogDir = "$AppDir\Logs"
if (Test-Path $LogDir) {
    $logFiles = Get-ChildItem "$LogDir\*.log" | Sort-Object LastWriteTime -Descending
    if ($logFiles.Count -gt 0) {
        $latestLog = $logFiles[0]
        Write-Host "[OK] Latest log file: $($latestLog.Name)" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Recent log entries (last 5):" -ForegroundColor Gray
        $lastLines = Get-Content $latestLog.FullName | Select-Object -Last 5
        foreach ($line in $lastLines) {
            if ($line -like "*Master password loaded*") {
                Write-Host "    $line" -ForegroundColor Green
            } elseif ($line -like "*ERROR*") {
                Write-Host "    $line" -ForegroundColor Red
            } else {
                Write-Host "    $line" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "[WARNING] No log files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "[WARNING] Logs directory does not exist" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Test completed. Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
