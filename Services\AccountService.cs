using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PasswordManager.Models;

namespace PasswordManager.Services
{
    /// <summary>
    /// Service layer for managing account operations with encryption
    /// </summary>
    public class AccountService
    {
        private string _masterPassword;

        /// <summary>
        /// Initializes the account service with a master password
        /// </summary>
        /// <param name="masterPassword">The master password for encryption/decryption</param>
        public AccountService(string masterPassword)
        {
            if (string.IsNullOrEmpty(masterPassword))
                throw new ArgumentException("Master password cannot be null or empty", nameof(masterPassword));

            _masterPassword = masterPassword;
        }

        /// <summary>
        /// Creates a new account with encrypted password
        /// </summary>
        /// <param name="websiteName">Name of the website</param>
        /// <param name="websiteUrl">URL of the website</param>
        /// <param name="username">Username for the account</param>
        /// <param name="password">Plain text password (will be encrypted)</param>
        /// <param name="notes">Optional notes</param>
        /// <returns>The created account with assigned ID</returns>
        public Account CreateAccount(string websiteName, string websiteUrl, string username, string password, string notes = "")
        {
            if (string.IsNullOrWhiteSpace(websiteName))
                throw new ArgumentException("Website name cannot be null or empty", nameof(websiteName));

            if (string.IsNullOrWhiteSpace(username))
                throw new ArgumentException("Username cannot be null or empty", nameof(username));

            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));

            try
            {
                // Encrypt the password
                var (encryptedPassword, salt) = EncryptionService.EncryptPassword(password, _masterPassword);

                // Create the account object
                var account = new Account(websiteName, websiteUrl, username)
                {
                    EncryptedPassword = encryptedPassword,
                    Salt = salt,
                    Notes = notes
                };

                // Save to database
                int accountId = DatabaseService.InsertAccount(account);
                account.Id = accountId;

                return account;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Updates an existing account
        /// </summary>
        /// <param name="account">The account to update</param>
        /// <param name="newPassword">New password (if provided, will be re-encrypted)</param>
        public void UpdateAccount(Account account, string newPassword = null)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));

            try
            {
                // If a new password is provided, encrypt it
                if (!string.IsNullOrEmpty(newPassword))
                {
                    var (encryptedPassword, salt) = EncryptionService.EncryptPassword(newPassword, _masterPassword);
                    account.EncryptedPassword = encryptedPassword;
                    account.Salt = salt;
                }

                // Update the modified date
                account.UpdateModifiedDate();

                // Save to database
                DatabaseService.UpdateAccount(account);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to update account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Deletes an account
        /// </summary>
        /// <param name="accountId">ID of the account to delete</param>
        public void DeleteAccount(int accountId)
        {
            try
            {
                DatabaseService.DeleteAccount(accountId);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to delete account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves all accounts
        /// </summary>
        /// <returns>List of all accounts</returns>
        public List<Account> GetAllAccounts()
        {
            try
            {
                return DatabaseService.GetAllAccounts();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve accounts: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves all accounts asynchronously for better UI responsiveness
        /// </summary>
        /// <returns>List of all accounts</returns>
        public async Task<List<Account>> GetAllAccountsAsync()
        {
            try
            {
                return await DatabaseService.GetAllAccountsAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve accounts: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates a new account asynchronously with encrypted password
        /// </summary>
        /// <param name="websiteName">Name of the website</param>
        /// <param name="websiteUrl">URL of the website</param>
        /// <param name="username">Username for the account</param>
        /// <param name="password">Plain text password (will be encrypted)</param>
        /// <param name="notes">Optional notes</param>
        /// <returns>The created account with assigned ID</returns>
        public async Task<Account> CreateAccountAsync(string websiteName, string websiteUrl, string username, string password, string notes = "")
        {
            using var tracker = PerformanceMonitor.StartTracking("AccountService.CreateAccount");

            if (string.IsNullOrWhiteSpace(websiteName))
                throw new ArgumentException("Website name cannot be null or empty", nameof(websiteName));

            if (string.IsNullOrWhiteSpace(username))
                throw new ArgumentException("Username cannot be null or empty", nameof(username));

            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));

            try
            {
                // Encrypt the password
                var (encryptedPassword, salt) = EncryptionService.EncryptPassword(password, _masterPassword);

                // Create the account object
                var account = new Account(websiteName, websiteUrl, username)
                {
                    EncryptedPassword = encryptedPassword,
                    Salt = salt,
                    Notes = notes
                };

                // Save to database
                int accountId = await DatabaseService.InsertAccountAsync(account);
                account.Id = accountId;

                return account;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to create account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Updates an existing account asynchronously
        /// </summary>
        /// <param name="account">The account to update</param>
        /// <param name="newPassword">New password (if provided, will be re-encrypted)</param>
        public async Task UpdateAccountAsync(Account account, string newPassword = null)
        {
            using var tracker = PerformanceMonitor.StartTracking("AccountService.UpdateAccount");

            if (account == null)
                throw new ArgumentNullException(nameof(account));

            try
            {
                // If a new password is provided, encrypt it
                if (!string.IsNullOrEmpty(newPassword))
                {
                    var (encryptedPassword, salt) = EncryptionService.EncryptPassword(newPassword, _masterPassword);
                    account.EncryptedPassword = encryptedPassword;
                    account.Salt = salt;
                }

                // Update the modified date
                account.UpdateModifiedDate();

                // Save to database
                await DatabaseService.UpdateAccountAsync(account);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to update account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves a specific account by ID
        /// </summary>
        /// <param name="accountId">ID of the account</param>
        /// <returns>The account or null if not found</returns>
        public Account GetAccountById(int accountId)
        {
            try
            {
                return DatabaseService.GetAccountById(accountId);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to retrieve account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Decrypts and returns the password for an account
        /// </summary>
        /// <param name="account">The account to decrypt the password for</param>
        /// <returns>The decrypted password</returns>
        public string GetDecryptedPassword(Account account)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));

            try
            {
                return EncryptionService.DecryptPassword(account.EncryptedPassword, account.Salt, _masterPassword);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to decrypt password: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Searches accounts by website name or username
        /// </summary>
        /// <param name="searchTerm">The term to search for</param>
        /// <returns>List of matching accounts</returns>
        public List<Account> SearchAccounts(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return GetAllAccounts();

            try
            {
                var allAccounts = GetAllAccounts();
                return allAccounts.Where(a =>
                    a.WebsiteName.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) >= 0 ||
                    a.Username.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) >= 0 ||
                    (!string.IsNullOrEmpty(a.WebsiteUrl) && a.WebsiteUrl.IndexOf(searchTerm, StringComparison.OrdinalIgnoreCase) >= 0)
                ).ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to search accounts: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Validates if an account with the same website and username already exists
        /// </summary>
        /// <param name="websiteName">Website name to check</param>
        /// <param name="username">Username to check</param>
        /// <param name="excludeAccountId">Account ID to exclude from the check (for updates)</param>
        /// <returns>True if a duplicate exists</returns>
        public bool IsDuplicateAccount(string websiteName, string username, int excludeAccountId = 0)
        {
            try
            {
                var allAccounts = GetAllAccounts();
                return allAccounts.Any(a =>
                    a.Id != excludeAccountId &&
                    string.Equals(a.WebsiteName, websiteName, StringComparison.OrdinalIgnoreCase) &&
                    string.Equals(a.Username, username, StringComparison.OrdinalIgnoreCase)
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to check for duplicate account: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets statistics about stored accounts
        /// </summary>
        /// <returns>Account statistics</returns>
        public AccountStatistics GetAccountStatistics()
        {
            try
            {
                var accounts = GetAllAccounts();
                return new AccountStatistics
                {
                    TotalAccounts = accounts.Count,
                    UniqueWebsites = accounts.Select(a => a.WebsiteName).Distinct().Count(),
                    OldestAccount = accounts.OrderBy(a => a.CreatedDate).FirstOrDefault()?.CreatedDate,
                    NewestAccount = accounts.OrderByDescending(a => a.CreatedDate).FirstOrDefault()?.CreatedDate,
                    LastModified = accounts.OrderByDescending(a => a.LastModified).FirstOrDefault()?.LastModified
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to get account statistics: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Disposes of sensitive data
        /// </summary>
        public void Dispose()
        {
            EncryptionService.SecureClearString(ref _masterPassword);
        }
    }

    /// <summary>
    /// Statistics about stored accounts
    /// </summary>
    public class AccountStatistics
    {
        public int TotalAccounts { get; set; }
        public int UniqueWebsites { get; set; }
        public DateTime? OldestAccount { get; set; }
        public DateTime? NewestAccount { get; set; }
        public DateTime? LastModified { get; set; }
    }
}
