@echo off
echo ========================================
echo Password Manager Authentication Verifier
echo ========================================
echo.

set "APP_DIR=%~dp0bin\Release\net8.0-windows\win-x64"
set "DB_PATH=%APP_DIR%\passwords.db"
set "LOG_DIR=%APP_DIR%\Logs"

echo Step 1: Checking Application Files
echo ----------------------------------
if exist "%APP_DIR%\PasswordManager.exe" (
    echo [OK] PasswordManager.exe found
) else (
    echo [ERROR] PasswordManager.exe not found
    goto :end
)

echo.
echo Step 2: Checking Database State
echo -------------------------------
if exist "%DB_PATH%" (
    echo [OK] Database file exists: passwords.db
    for %%A in ("%DB_PATH%") do echo [INFO] Database size: %%~zA bytes
    for %%A in ("%DB_PATH%") do echo [INFO] Last modified: %%~tA
) else (
    echo [INFO] Database file does not exist - this is first-time setup
)

echo.
echo Step 3: Checking Log Files
echo --------------------------
if exist "%LOG_DIR%" (
    echo [OK] Logs directory exists
    for /f "delims=" %%A in ('dir /b /o-d "%LOG_DIR%\*.log" 2^>nul') do (
        echo [INFO] Latest log file: %%A
        goto :found_log
    )
    echo [WARNING] No log files found
    :found_log
) else (
    echo [WARNING] Logs directory does not exist
)

echo.
echo Step 4: Testing Application Launch
echo ----------------------------------
echo [INFO] Attempting to launch Password Manager...
echo [INFO] Please follow these steps in the application:
echo.
echo   1. Look at the "Remember master password" checkbox
echo   2. If checked, UNCHECK it
echo   3. Clear the password field completely
echo   4. Type: 123456medoissaA@@
echo   5. Click Unlock
echo   6. Note any error messages
echo.
echo [INFO] Launching application now...
start "" "%APP_DIR%\PasswordManager.exe"

echo.
echo Step 5: Waiting for Application Response
echo ----------------------------------------
echo [INFO] Application launched. Please try authentication.
echo [INFO] After attempting authentication, press any key to continue...
pause >nul

echo.
echo Step 6: Checking Latest Log Entries
echo -----------------------------------
if exist "%LOG_DIR%" (
    for /f "delims=" %%A in ('dir /b /o-d "%LOG_DIR%\*.log" 2^>nul') do (
        echo [INFO] Checking latest entries in: %%A
        powershell -Command "Get-Content '%LOG_DIR%\%%A' | Select-Object -Last 10"
        goto :checked_log
    )
    :checked_log
) else (
    echo [WARNING] Cannot check logs - directory missing
)

echo.
echo Step 7: Next Steps Based on Results
echo -----------------------------------
echo If you saw "Invalid master password" in the logs:
echo   - The remember password conflict still exists
echo   - Try the database reset option below
echo.
echo If you saw "Authentication successful":
echo   - The issue is resolved!
echo.
echo If no new log entries appeared:
echo   - The application may not be responding
echo   - Try running as administrator
echo.

:end
echo.
echo Press any key to exit...
pause >nul
