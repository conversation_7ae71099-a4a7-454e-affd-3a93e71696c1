# Start Password Manager - Robust Launcher
# This script provides multiple launch methods for the Password Manager

param(
    [switch]$SkipBuild,
    [switch]$DirectLaunch,
    [switch]$Help
)

if ($Help) {
    Write-Host ""
    Write-Host "Password Manager Launcher" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor White
    Write-Host "  .\Start-PasswordManager.ps1           # Full build and launch"
    Write-Host "  .\Start-PasswordManager.ps1 -SkipBuild # Launch existing executable"
    Write-Host "  .\Start-PasswordManager.ps1 -DirectLaunch # Direct executable launch"
    Write-Host "  .\Start-PasswordManager.ps1 -Help     # Show this help"
    Write-Host ""
    exit 0
}

Write-Host ""
Write-Host "Password Manager Launcher" -ForegroundColor Magenta
Write-Host "========================" -ForegroundColor Magenta
Write-Host ""

# Define paths
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$rootDir = Split-Path -Parent $scriptDir
$projectFile = Join-Path $rootDir "PasswordManager-Modern.csproj"
$exePath = Join-Path $rootDir "bin\Release\net8.0-windows\win-x64\PasswordManager.exe"
$altExePath = $exePath

try {
    if ($DirectLaunch) {
        Write-Host "[DIRECT] Launching Password Manager directly..." -ForegroundColor Yellow

        if (Test-Path $exePath) {
            $process = Start-Process -FilePath $exePath -PassThru
            Write-Host "[SUCCESS] Password Manager started! (PID: $($process.Id))" -ForegroundColor Green
        }
        elseif (Test-Path $altExePath) {
            $process = Start-Process -FilePath $altExePath -PassThru
            Write-Host "[SUCCESS] Password Manager started! (PID: $($process.Id))" -ForegroundColor Green
        }
        else {
            Write-Host "[ERROR] Executable not found. Run without -DirectLaunch to build first." -ForegroundColor Red
            exit 1
        }
        exit 0
    }

    if (-not $SkipBuild) {
        # Check .NET
        Write-Host "[INFO] Checking .NET 8..." -ForegroundColor Cyan
        $dotnetVersion = & dotnet --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw ".NET 8 SDK not found. Please install .NET 8.0 SDK."
        }
        Write-Host "[SUCCESS] .NET version: $dotnetVersion" -ForegroundColor Green

        # Check project
        Write-Host "[INFO] Verifying project..." -ForegroundColor Cyan
        if (-not (Test-Path $projectFile)) {
            throw "Project file not found: $projectFile"
        }
        Write-Host "[SUCCESS] Project file found" -ForegroundColor Green

        # Build
        Write-Host "[INFO] Building application..." -ForegroundColor Cyan
        & dotnet build $projectFile --configuration Release --verbosity minimal
        if ($LASTEXITCODE -ne 0) {
            throw "Build failed"
        }
        Write-Host "[SUCCESS] Build completed" -ForegroundColor Green
    }

    # Check executable
    Write-Host "[INFO] Checking executable..." -ForegroundColor Cyan
    if (-not (Test-Path $exePath)) {
        throw "Executable not found at: $exePath"
    }
    Write-Host "[SUCCESS] Executable ready" -ForegroundColor Green

    # Launch
    Write-Host "[INFO] Starting Password Manager..." -ForegroundColor Cyan
    $process = Start-Process -FilePath $exePath -PassThru

    # Verify launch
    Start-Sleep -Seconds 2
    if ($process.HasExited) {
        Write-Host "[ERROR] Application exited immediately (Exit code: $($process.ExitCode))" -ForegroundColor Red
        Write-Host ""
        Write-Host "Possible solutions:" -ForegroundColor Yellow
        Write-Host "1. Install .NET 8 Desktop Runtime" -ForegroundColor Gray
        Write-Host "2. Check antivirus software" -ForegroundColor Gray
        Write-Host "3. Run as administrator" -ForegroundColor Gray
        exit 1
    }
    else {
        Write-Host "[SUCCESS] Password Manager is running! (PID: $($process.Id))" -ForegroundColor Green
        Write-Host ""
        Write-Host "Features Available:" -ForegroundColor Cyan
        Write-Host "- Master password protection" -ForegroundColor Gray
        Write-Host "- AES-256 encryption" -ForegroundColor Gray
        Write-Host "- Tabbed interface" -ForegroundColor Gray
        Write-Host "- Password generator" -ForegroundColor Gray
        Write-Host "- Local SQLite storage" -ForegroundColor Gray
        Write-Host ""
        Write-Host "The application window should be visible now." -ForegroundColor Green
    }

}
catch {
    Write-Host ""
    Write-Host "[ERROR] $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Quick fixes:" -ForegroundColor Yellow
    Write-Host "1. Try: .\Start-PasswordManager.ps1 -DirectLaunch" -ForegroundColor Gray
    Write-Host "2. Install .NET 8: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Gray
    Write-Host "3. Run as administrator" -ForegroundColor Gray
    exit 1
}

Write-Host ""
