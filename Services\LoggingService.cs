using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace PasswordManager.Services
{
    /// <summary>
    /// High-performance logging service with async operations and memory optimization
    /// </summary>
    public static class LoggingService
    {
        private static readonly string LogDirectory = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, "Logs");
        
        private static readonly string LogFilePath = Path.Combine(
            LogDirectory, $"PasswordManager_{DateTime.Now:yyyy-MM-dd}.log");
        
        private static readonly SemaphoreSlim _logSemaphore = new(1, 1);
        private static volatile bool _isInitialized = false;
        private static readonly object _initLock = new();

        public enum LogLevel
        {
            Debug = 0,
            Info = 1,
            Warning = 2,
            Error = 3,
            Critical = 4
        }

        /// <summary>
        /// Initializes the logging service
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            lock (_initLock)
            {
                if (_isInitialized) return;

                try
                {
                    if (!Directory.Exists(LogDirectory))
                    {
                        Directory.CreateDirectory(LogDirectory);
                    }

                    // Clean up old log files (keep only last 30 days)
                    CleanupOldLogs();
                    
                    _isInitialized = true;
                    LogInfo("Logging service initialized successfully");
                }
                catch (Exception ex)
                {
                    // If logging fails, we can't log the error, so we'll just continue
                    System.Diagnostics.Debug.WriteLine($"Failed to initialize logging: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Logs a debug message
        /// </summary>
        public static void LogDebug(string message, Exception exception = null)
        {
            _ = LogAsync(LogLevel.Debug, message, exception);
        }

        /// <summary>
        /// Logs an info message
        /// </summary>
        public static void LogInfo(string message, Exception exception = null)
        {
            _ = LogAsync(LogLevel.Info, message, exception);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        public static void LogWarning(string message, Exception exception = null)
        {
            _ = LogAsync(LogLevel.Warning, message, exception);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        public static void LogError(string message, Exception exception = null)
        {
            _ = LogAsync(LogLevel.Error, message, exception);
        }

        /// <summary>
        /// Logs a critical message
        /// </summary>
        public static void LogCritical(string message, Exception exception = null)
        {
            _ = LogAsync(LogLevel.Critical, message, exception);
        }

        /// <summary>
        /// Logs performance metrics
        /// </summary>
        public static void LogPerformance(string operation, TimeSpan duration, string additionalInfo = null)
        {
            var message = $"PERFORMANCE: {operation} completed in {duration.TotalMilliseconds:F2}ms";
            if (!string.IsNullOrEmpty(additionalInfo))
            {
                message += $" - {additionalInfo}";
            }
            LogInfo(message);
        }

        /// <summary>
        /// Logs security events
        /// </summary>
        public static void LogSecurity(string securityEvent, string details = null)
        {
            var message = $"SECURITY: {securityEvent}";
            if (!string.IsNullOrEmpty(details))
            {
                message += $" - {details}";
            }
            LogWarning(message);
        }

        /// <summary>
        /// Asynchronously logs a message
        /// </summary>
        private static async Task LogAsync(LogLevel level, string message, Exception exception = null)
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            try
            {
                await _logSemaphore.WaitAsync();

                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var threadId = Thread.CurrentThread.ManagedThreadId;
                var logEntry = $"[{timestamp}] [{level.ToString().ToUpper()}] [Thread-{threadId}] {message}";

                if (exception != null)
                {
                    logEntry += $"\nException: {exception.GetType().Name}: {exception.Message}";
                    logEntry += $"\nStack Trace: {exception.StackTrace}";
                    
                    if (exception.InnerException != null)
                    {
                        logEntry += $"\nInner Exception: {exception.InnerException.GetType().Name}: {exception.InnerException.Message}";
                    }
                }

                logEntry += Environment.NewLine;

                await File.AppendAllTextAsync(LogFilePath, logEntry);

                // Also output to debug console for development
                System.Diagnostics.Debug.WriteLine(logEntry.TrimEnd());
            }
            catch (Exception ex)
            {
                // If logging fails, output to debug console
                System.Diagnostics.Debug.WriteLine($"Logging failed: {ex.Message}");
            }
            finally
            {
                _logSemaphore.Release();
            }
        }

        /// <summary>
        /// Cleans up old log files
        /// </summary>
        private static void CleanupOldLogs()
        {
            try
            {
                if (!Directory.Exists(LogDirectory)) return;

                var cutoffDate = DateTime.Now.AddDays(-30);
                var logFiles = Directory.GetFiles(LogDirectory, "PasswordManager_*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to cleanup old logs: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current log file path
        /// </summary>
        public static string GetLogFilePath()
        {
            return LogFilePath;
        }

        /// <summary>
        /// Flushes all pending log operations
        /// </summary>
        public static async Task FlushAsync()
        {
            await _logSemaphore.WaitAsync();
            try
            {
                // All operations are already flushed since we use AppendAllTextAsync
                await Task.CompletedTask;
            }
            finally
            {
                _logSemaphore.Release();
            }
        }
    }
}
