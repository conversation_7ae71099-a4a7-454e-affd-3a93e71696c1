using System;
using PasswordManager.Services;

namespace PasswordManager
{
    /// <summary>
    /// Test class to verify the specific password "123456medoissaA@@"
    /// </summary>
    public static class SpecificPasswordTest
    {
        public static void TestPassword()
        {
            string testPassword = "123456medoissa<PERSON>@@";
            
            Console.WriteLine("🔍 Testing Password: 123456medoissaA@@");
            Console.WriteLine("=====================================\n");

            // Test 1: Validation Requirements
            TestValidationRequirements(testPassword);

            // Test 2: Encryption/Hashing
            TestEncryptionHashing(testPassword);

            // Test 3: Character Analysis
            TestCharacterAnalysis(testPassword);

            // Test 4: SQL Injection Check
            TestSqlInjectionCheck(testPassword);

            Console.WriteLine("\n✅ Password testing completed!");
        }

        private static void TestValidationRequirements(string password)
        {
            Console.WriteLine("🔧 Test 1: Validation Requirements");
            
            var result = ValidationService.ValidateMasterPassword(password);
            
            Console.WriteLine($"Password: {password}");
            Console.WriteLine($"Length: {password.Length} characters");
            Console.WriteLine($"Is Valid: {(result.IsValid ? "✅ YES" : "❌ NO")}");
            
            if (result.Errors.Count > 0)
            {
                Console.WriteLine("❌ Errors:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"  - {error}");
                }
            }
            
            if (result.Warnings.Count > 0)
            {
                Console.WriteLine("⚠️ Warnings:");
                foreach (var warning in result.Warnings)
                {
                    Console.WriteLine($"  - {warning}");
                }
            }
            
            if (result.IsValid)
            {
                Console.WriteLine("✅ Password meets all master password requirements!");
            }
            
            Console.WriteLine();
        }

        private static void TestEncryptionHashing(string password)
        {
            Console.WriteLine("🔧 Test 2: Encryption/Hashing");
            
            try
            {
                // Test password hashing
                var (hash, salt) = EncryptionService.HashMasterPassword(password);
                Console.WriteLine($"✅ Password hashing successful");
                Console.WriteLine($"Hash length: {hash.Length} characters");
                Console.WriteLine($"Salt length: {salt.Length} characters");
                Console.WriteLine($"Hash preview: {hash.Substring(0, Math.Min(10, hash.Length))}...");
                Console.WriteLine($"Salt preview: {salt.Substring(0, Math.Min(10, salt.Length))}...");

                // Test password verification
                bool isValid = EncryptionService.VerifyMasterPassword(password, hash, salt);
                Console.WriteLine($"✅ Password verification: {(isValid ? "SUCCESS" : "FAILED")}");

                // Test with wrong password
                bool isInvalid = EncryptionService.VerifyMasterPassword("wrongpassword", hash, salt);
                Console.WriteLine($"✅ Wrong password rejection: {(!isInvalid ? "SUCCESS" : "FAILED")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Encryption/Hashing failed: {ex.Message}");
            }
            
            Console.WriteLine();
        }

        private static void TestCharacterAnalysis(string password)
        {
            Console.WriteLine("🔧 Test 3: Character Analysis");
            
            Console.WriteLine($"Password: {password}");
            Console.WriteLine($"Character breakdown:");
            
            for (int i = 0; i < password.Length; i++)
            {
                char c = password[i];
                string type = GetCharacterType(c);
                Console.WriteLine($"  [{i:D2}] '{c}' - {type} (ASCII: {(int)c})");
            }
            
            // Character type summary
            int digits = 0, upper = 0, lower = 0, special = 0;
            foreach (char c in password)
            {
                if (char.IsDigit(c)) digits++;
                else if (char.IsUpper(c)) upper++;
                else if (char.IsLower(c)) lower++;
                else special++;
            }
            
            Console.WriteLine($"\nCharacter Summary:");
            Console.WriteLine($"  Digits: {digits} ✅");
            Console.WriteLine($"  Uppercase: {upper} ✅");
            Console.WriteLine($"  Lowercase: {lower} ✅");
            Console.WriteLine($"  Special: {special} ✅");
            
            Console.WriteLine();
        }

        private static void TestSqlInjectionCheck(string password)
        {
            Console.WriteLine("🔧 Test 4: SQL Injection Check");
            
            // Check if password contains any dangerous patterns
            var dangerousPatterns = new[]
            {
                "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_", "exec", "execute",
                "select", "insert", "update", "delete", "drop", "create", "alter",
                "union", "script", "javascript:", "vbscript:", "onload", "onerror"
            };

            bool hasDangerousPatterns = false;
            var lowerPassword = password.ToLowerInvariant();
            
            foreach (var pattern in dangerousPatterns)
            {
                if (lowerPassword.Contains(pattern))
                {
                    Console.WriteLine($"⚠️ Contains pattern: {pattern}");
                    hasDangerousPatterns = true;
                }
            }
            
            if (!hasDangerousPatterns)
            {
                Console.WriteLine("✅ No dangerous SQL injection patterns found");
            }
            
            Console.WriteLine();
        }

        private static string GetCharacterType(char c)
        {
            if (char.IsDigit(c)) return "Digit";
            if (char.IsUpper(c)) return "Uppercase Letter";
            if (char.IsLower(c)) return "Lowercase Letter";
            if (char.IsLetter(c)) return "Letter";
            if (char.IsWhiteSpace(c)) return "Whitespace";
            return "Special Character";
        }
    }
}
