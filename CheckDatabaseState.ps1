# Password Manager Database State Checker
Write-Host "Password Manager Database State Analysis" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan
Write-Host ""

$AppDir = ".\bin\Release\net8.0-windows\win-x64"
$DbPath = "$AppDir\passwords.db"
$LogDir = "$AppDir\Logs"

# Check if database exists
Write-Host "Database Analysis:" -ForegroundColor Yellow
if (Test-Path $DbPath) {
    $dbInfo = Get-Item $DbPath
    Write-Host "[OK] Database file exists" -ForegroundColor Green
    Write-Host "    Path: $DbPath" -ForegroundColor Gray
    Write-Host "    Size: $($dbInfo.Length) bytes" -ForegroundColor Gray
    Write-Host "    Created: $($dbInfo.CreationTime)" -ForegroundColor Gray
    Write-Host "    Modified: $($dbInfo.LastWriteTime)" -ForegroundColor Gray
    
    # Try to read database contents
    try {
        # Load SQLite assembly if available
        Add-Type -Path "$AppDir\Microsoft.Data.Sqlite.dll" -ErrorAction SilentlyContinue
        
        $connectionString = "Data Source=$DbPath"
        $connection = New-Object Microsoft.Data.Sqlite.SqliteConnection($connectionString)
        $connection.Open()
        
        Write-Host ""
        Write-Host "Database Contents:" -ForegroundColor Yellow
        
        # Check Settings table
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT Key, Value FROM Settings"
        $reader = $command.ExecuteReader()
        
        $hasSettings = $false
        while ($reader.Read()) {
            $hasSettings = $true
            $key = $reader["Key"]
            $value = $reader["Value"]
            
            if ($key -like "*Password*") {
                $valuePreview = $value.Substring(0, [Math]::Min(10, $value.Length)) + "..."
                Write-Host "    $key : $valuePreview (length: $($value.Length))" -ForegroundColor Gray
            } else {
                Write-Host "    $key : $value" -ForegroundColor Gray
            }
        }
        
        if (-not $hasSettings) {
            Write-Host "    [WARNING] No settings found in database" -ForegroundColor Red
        }
        
        $reader.Close()
        $connection.Close()
        
    } catch {
        Write-Host "    [ERROR] Cannot read database contents: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "[INFO] Database file does not exist - this is first-time setup" -ForegroundColor Blue
}

Write-Host ""

# Check log files
Write-Host "Log File Analysis:" -ForegroundColor Yellow
if (Test-Path $LogDir) {
    $logFiles = Get-ChildItem "$LogDir\*.log" | Sort-Object LastWriteTime -Descending
    if ($logFiles.Count -gt 0) {
        $latestLog = $logFiles[0]
        Write-Host "[OK] Latest log file: $($latestLog.Name)" -ForegroundColor Green
        Write-Host "    Modified: $($latestLog.LastWriteTime)" -ForegroundColor Gray
        
        # Show last 10 lines
        Write-Host ""
        Write-Host "Latest Log Entries:" -ForegroundColor Yellow
        $lastLines = Get-Content $latestLog.FullName | Select-Object -Last 10
        foreach ($line in $lastLines) {
            if ($line -like "*ERROR*") {
                Write-Host "    $line" -ForegroundColor Red
            } elseif ($line -like "*SUCCESS*" -or $line -like "*Authentication successful*") {
                Write-Host "    $line" -ForegroundColor Green
            } else {
                Write-Host "    $line" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "[WARNING] No log files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "[WARNING] Logs directory does not exist" -ForegroundColor Yellow
}

Write-Host ""

# Provide recommendations
Write-Host "Recommendations:" -ForegroundColor Cyan
if (Test-Path $DbPath) {
    Write-Host "1. Database exists - this is NOT first-time setup" -ForegroundColor White
    Write-Host "2. Check if 'Remember master password' checkbox is causing conflict" -ForegroundColor White
    Write-Host "3. If authentication still fails, consider database reset" -ForegroundColor White
} else {
    Write-Host "1. Database missing - this IS first-time setup" -ForegroundColor White
    Write-Host "2. Enter your password to create new master password" -ForegroundColor White
}

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Run Password Manager" -ForegroundColor White
Write-Host "2. UNCHECK 'Remember master password' if checked" -ForegroundColor White
Write-Host "3. CLEAR password field completely" -ForegroundColor White
Write-Host "4. TYPE manually: 123456medoissaA@@" -ForegroundColor White
Write-Host "5. Click Unlock" -ForegroundColor White

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
