@echo off
echo ==========================================
echo Password Manager Database Reset Utility
echo ==========================================
echo.
echo WARNING: This will delete all stored passwords!
echo Only proceed if you have no important data or have backups.
echo.

set "APP_DIR=%~dp0bin\Release\net8.0-windows\win-x64"
set "DB_PATH=%APP_DIR%\passwords.db"
set "BACKUP_PATH=%APP_DIR%\passwords.db.backup"

echo Current database status:
if exist "%DB_PATH%" (
    echo [FOUND] Database file exists
    for %%A in ("%DB_PATH%") do echo [INFO] Size: %%~zA bytes
    for %%A in ("%DB_PATH%") do echo [INFO] Modified: %%~tA
) else (
    echo [NOT FOUND] Database file does not exist
    echo [INFO] This appears to be a fresh installation
    goto :end
)

echo.
set /p "CONFIRM=Do you want to reset the database? This will delete all data! (yes/no): "
if /i not "%CONFIRM%"=="yes" (
    echo [CANCELLED] Database reset cancelled by user
    goto :end
)

echo.
echo Step 1: Stopping Password Manager
echo ---------------------------------
echo [INFO] Attempting to close Password Manager processes...
taskkill /f /im PasswordManager.exe >nul 2>&1
if %errorlevel%==0 (
    echo [OK] Password Manager processes stopped
) else (
    echo [INFO] No Password Manager processes were running
)

echo.
echo Step 2: Creating Backup
echo -----------------------
if exist "%DB_PATH%" (
    copy "%DB_PATH%" "%BACKUP_PATH%" >nul
    if %errorlevel%==0 (
        echo [OK] Database backed up to: passwords.db.backup
    ) else (
        echo [ERROR] Failed to create backup
        goto :end
    )
) else (
    echo [INFO] No database to backup
)

echo.
echo Step 3: Deleting Database
echo -------------------------
if exist "%DB_PATH%" (
    del "%DB_PATH%"
    if %errorlevel%==0 (
        echo [OK] Database deleted successfully
    ) else (
        echo [ERROR] Failed to delete database
        goto :end
    )
) else (
    echo [INFO] Database already deleted
)

echo.
echo Step 4: Clearing Logs (Optional)
echo --------------------------------
set /p "CLEAR_LOGS=Do you want to clear log files? (yes/no): "
if /i "%CLEAR_LOGS%"=="yes" (
    if exist "%APP_DIR%\Logs\*.log" (
        del "%APP_DIR%\Logs\*.log"
        echo [OK] Log files cleared
    ) else (
        echo [INFO] No log files to clear
    )
)

echo.
echo Step 5: Reset Complete
echo =====================
echo [SUCCESS] Database reset completed!
echo.
echo Next steps:
echo 1. Launch Password Manager
echo 2. You should see: "Welcome! Please set up your master password"
echo 3. Enter your password: 123456medoissaA@@
echo 4. Choose whether to remember the password
echo 5. Click Unlock
echo.
echo If you want to restore your data later:
echo - Close Password Manager
echo - Delete the new passwords.db
echo - Rename passwords.db.backup to passwords.db
echo - Restart Password Manager
echo.

:end
echo Press any key to exit...
pause >nul
