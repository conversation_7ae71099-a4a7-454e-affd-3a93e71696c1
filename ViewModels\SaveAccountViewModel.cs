using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using PasswordManager.Models;
using PasswordManager.Services;
using PasswordManager.Utilities;

namespace PasswordManager.ViewModels
{
    /// <summary>
    /// ViewModel for the Save Account tab
    /// </summary>
    public class SaveAccountViewModel : BaseViewModel
    {
        private readonly AccountService _accountService;

        #region Properties

        private string _websiteName;
        public string WebsiteName
        {
            get => _websiteName;
            set => SetProperty(ref _websiteName, value, () => ValidateInput());
        }

        private string _websiteUrl;
        public string WebsiteUrl
        {
            get => _websiteUrl;
            set => SetProperty(ref _websiteUrl, value);
        }

        private string _username;
        public string Username
        {
            get => _username;
            set => SetProperty(ref _username, value, () => ValidateInput());
        }

        private string _password;
        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value, () =>
            {
                ValidateInput();
                // Notify that password was updated programmatically
                OnPasswordUpdated?.Invoke(value);
            });
        }

        /// <summary>
        /// Event to notify when password is updated programmatically
        /// </summary>
        public event Action<string> OnPasswordUpdated;

        private string _notes;
        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        private Account _selectedAccount;
        public Account SelectedAccount
        {
            get => _selectedAccount;
            set => SetProperty(ref _selectedAccount, value, () => LoadSelectedAccount());
        }

        private ObservableCollection<Account> _accounts;
        public ObservableCollection<Account> Accounts
        {
            get => _accounts;
            set => SetProperty(ref _accounts, value);
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value, () => FilterAccounts());
        }

        private bool _isPasswordVisible;
        public bool IsPasswordVisible
        {
            get => _isPasswordVisible;
            set => SetProperty(ref _isPasswordVisible, value);
        }

        private bool _isEditMode;
        public bool IsEditMode
        {
            get => _isEditMode;
            set => SetProperty(ref _isEditMode, value);
        }

        private bool _canSave;
        public bool CanSave
        {
            get => _canSave;
            set => SetProperty(ref _canSave, value);
        }

        #endregion

        #region Commands

        public ICommand SaveAccountCommand { get; }
        public ICommand UpdateAccountCommand { get; }
        public ICommand DeleteAccountCommand { get; }
        public ICommand EditAccountCommand { get; }
        public ICommand CancelEditCommand { get; }
        public ICommand ClearFormCommand { get; }
        public ICommand TogglePasswordVisibilityCommand { get; }
        public ICommand GeneratePasswordCommand { get; }
        public ICommand CopyPasswordCommand { get; }
        public ICommand CopyUsernameCommand { get; }

        #endregion

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="accountService">Account service for data operations</param>
        public SaveAccountViewModel(AccountService accountService)
        {
            _accountService = accountService ?? throw new ArgumentNullException(nameof(accountService));

            // Initialize collections
            Accounts = new ObservableCollection<Account>();

            // Initialize commands
            SaveAccountCommand = new SimpleRelayCommand(SaveAccount, () => CanSave && !IsEditMode);
            UpdateAccountCommand = new SimpleRelayCommand(UpdateAccount, () => CanSave && IsEditMode);
            DeleteAccountCommand = new RelayCommand<Account>(DeleteAccount, account => account != null);
            EditAccountCommand = new RelayCommand<Account>(EditAccount, account => account != null);
            CancelEditCommand = new SimpleRelayCommand(CancelEdit, () => IsEditMode);
            ClearFormCommand = new SimpleRelayCommand(ClearForm);
            TogglePasswordVisibilityCommand = new SimpleRelayCommand(TogglePasswordVisibility);
            GeneratePasswordCommand = new SimpleRelayCommand(GeneratePassword);
            CopyPasswordCommand = new RelayCommand<Account>(CopyPassword, account => account != null);
            CopyUsernameCommand = new RelayCommand<Account>(CopyUsername, account => account != null);

            // Load accounts
            LoadAccounts();
        }

        #region Command Implementations

        private void SaveAccount()
        {
            ExecuteSafely(() =>
            {
                // Check for duplicates
                if (_accountService.IsDuplicateAccount(WebsiteName, Username))
                {
                    SetError("An account with this website and username already exists.");
                    return;
                }

                // Create new account
                var account = _accountService.CreateAccount(WebsiteName, WebsiteUrl, Username, Password, Notes);

                // Add to collection
                Accounts.Add(account);

                // Clear form and show success
                ClearForm();
                SetSuccess("Account saved successfully!");

            }, "Failed to save account");
        }

        private void UpdateAccount()
        {
            ExecuteSafely(() =>
            {
                if (SelectedAccount == null) return;

                // Check for duplicates (excluding current account)
                if (_accountService.IsDuplicateAccount(WebsiteName, Username, SelectedAccount.Id))
                {
                    SetError("An account with this website and username already exists.");
                    return;
                }

                // Update account properties
                SelectedAccount.WebsiteName = WebsiteName;
                SelectedAccount.WebsiteUrl = WebsiteUrl;
                SelectedAccount.Username = Username;
                SelectedAccount.Notes = Notes;

                // Update in service (will re-encrypt password if provided)
                _accountService.UpdateAccount(SelectedAccount, !string.IsNullOrEmpty(Password) ? Password : null);

                // Exit edit mode and show success
                IsEditMode = false;
                ClearForm();
                SetSuccess("Account updated successfully!");

            }, "Failed to update account");
        }

        private void DeleteAccount(Account account)
        {
            if (account == null) return;

            var result = MessageBox.Show(
                $"Are you sure you want to delete the account for {account.WebsiteName} - {account.Username}?",
                "Confirm Delete",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ExecuteSafely(() =>
                {
                    _accountService.DeleteAccount(account.Id);
                    Accounts.Remove(account);

                    if (SelectedAccount == account)
                    {
                        SelectedAccount = null;
                        ClearForm();
                    }

                    SetSuccess("Account deleted successfully!");

                }, "Failed to delete account");
            }
        }

        private void EditAccount(Account account)
        {
            if (account == null) return;

            SelectedAccount = account;
            LoadSelectedAccount();
            IsEditMode = true;
        }

        private void CancelEdit()
        {
            IsEditMode = false;
            ClearForm();
            SelectedAccount = null;
        }

        private void ClearForm()
        {
            WebsiteName = string.Empty;
            WebsiteUrl = string.Empty;
            Username = string.Empty;
            Password = string.Empty;
            Notes = string.Empty;
            IsPasswordVisible = false;
            IsEditMode = false;
            SelectedAccount = null;
            ClearMessages();
        }

        private void TogglePasswordVisibility()
        {
            IsPasswordVisible = !IsPasswordVisible;
        }

        private void GeneratePassword()
        {
            Password = EncryptionService.GenerateSecurePassword(16, true);
            SetSuccess("Secure password generated!");
        }

        private void CopyPassword(Account account)
        {
            if (account == null) return;

            ExecuteSafely(() =>
            {
                var decryptedPassword = _accountService.GetDecryptedPassword(account);
                Clipboard.SetText(decryptedPassword);
                SetSuccess("Password copied to clipboard!");

            }, "Failed to copy password");
        }

        private void CopyUsername(Account account)
        {
            if (account == null) return;

            Clipboard.SetText(account.Username);
            SetSuccess("Username copied to clipboard!");
        }

        #endregion

        #region Helper Methods

        private void LoadAccounts()
        {
            _ = LoadAccountsAsync();
        }

        private async Task LoadAccountsAsync()
        {
            await ExecuteSafelyAsync(async () =>
            {
                var accounts = await _accountService.GetAllAccountsAsync();

                // Update UI on main thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    Accounts.Clear();
                    foreach (var account in accounts)
                    {
                        Accounts.Add(account);
                    }
                });
            }, "Failed to load accounts");
        }

        private void LoadSelectedAccount()
        {
            if (SelectedAccount == null) return;

            WebsiteName = SelectedAccount.WebsiteName;
            WebsiteUrl = SelectedAccount.WebsiteUrl;
            Username = SelectedAccount.Username;
            Notes = SelectedAccount.Notes;
            Password = string.Empty; // Don't load password for security
        }

        private void FilterAccounts()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                LoadAccounts();
                return;
            }

            ExecuteSafely(() =>
            {
                var filteredAccounts = _accountService.SearchAccounts(SearchText);
                Accounts.Clear();
                foreach (var account in filteredAccounts)
                {
                    Accounts.Add(account);
                }
            }, "Failed to search accounts");
        }

        private void ValidateInput()
        {
            CanSave = !string.IsNullOrWhiteSpace(WebsiteName) &&
                     !string.IsNullOrWhiteSpace(Username) &&
                     !string.IsNullOrWhiteSpace(Password);

            // Notify that command CanExecute state may have changed
            System.Windows.Input.CommandManager.InvalidateRequerySuggested();
        }

        #endregion

        public override void Dispose()
        {
            _accountService?.Dispose();
            base.Dispose();
        }
    }
}
