# Launch Password Manager WPF Application
# Simple launcher script for Password Manager

Write-Host ""
Write-Host "Password Manager - WPF Application Launcher" -ForegroundColor Magenta
Write-Host "===========================================" -ForegroundColor Magenta
Write-Host ""

function Write-Success($msg) { Write-Host "[SUCCESS] $msg" -ForegroundColor Green }
function Write-Info($msg) { Write-Host "[INFO] $msg" -ForegroundColor Cyan }
function Write-Warning($msg) { Write-Host "[WARNING] $msg" -ForegroundColor Yellow }
function Write-Error($msg) { Write-Host "[ERROR] $msg" -ForegroundColor Red }

try {
    # Check .NET 8
    Write-Info "Checking .NET 8 availability..."
    $dotnetVersion = & dotnet --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw ".NET 8 SDK not found. Please install .NET 8.0 SDK."
    }
    Write-Success ".NET version: $dotnetVersion"

    # Check project file
    Write-Info "Verifying project files..."
    $scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $rootDir = Split-Path -Parent $scriptDir
    $projectFile = Join-Path $rootDir "PasswordManager-Modern.csproj"
    if (-not (Test-Path $projectFile)) {
        throw "PasswordManager-Modern.csproj not found."
    }
    Write-Success "Project file found"

    # Build the application
    Write-Info "Building Password Manager WPF application..."
    & dotnet build $projectFile --configuration Release --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Success "Build completed successfully"

    # Check executable
    $exePath = Join-Path $rootDir "bin\Release\net8.0-windows\win-x64\PasswordManager.exe"
    if (-not (Test-Path $exePath)) {
        throw "Executable not found at: $exePath"
    }
    Write-Success "Executable ready: $exePath"

    # Launch the application
    Write-Info "Launching Password Manager WPF application..."
    Write-Host ""
    Write-Host "Starting Password Manager..." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "DATABASE FIX APPLIED: Connection string updated for Microsoft.Data.Sqlite compatibility" -ForegroundColor Green
    Write-Host ""

    $process = Start-Process -FilePath $exePath -PassThru

    # Wait a moment to check if it started successfully
    Start-Sleep -Seconds 3

    if ($process.HasExited) {
        Write-Error "Application exited immediately. Exit code: $($process.ExitCode)"
        Write-Host ""
        Write-Host "Possible issues:" -ForegroundColor Yellow
        Write-Host "- Missing .NET 8 Desktop Runtime" -ForegroundColor Gray
        Write-Host "- Display/graphics driver issues" -ForegroundColor Gray
        Write-Host "- Antivirus blocking the application" -ForegroundColor Gray
    }
    else {
        Write-Success "Password Manager launched successfully! (PID: $($process.Id))"
        Write-Host ""
        Write-Host "SUCCESS! Password Manager is now running!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Application Features:" -ForegroundColor Cyan
        Write-Host "- Master password authentication" -ForegroundColor Gray
        Write-Host "- AES-256 encrypted password storage" -ForegroundColor Gray
        Write-Host "- Tabbed interface with Arabic/English support" -ForegroundColor Gray
        Write-Host "- SQLite database for local storage" -ForegroundColor Gray
        Write-Host "- Secure password generator" -ForegroundColor Gray
        Write-Host "- Copy to clipboard functionality" -ForegroundColor Gray
        Write-Host "- Search and filter accounts" -ForegroundColor Gray
        Write-Host "- Professional MVVM architecture" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Getting Started:" -ForegroundColor Cyan
        Write-Host "1. Set up your master password on first run" -ForegroundColor Gray
        Write-Host "2. Navigate to the Save Account tab" -ForegroundColor Gray
        Write-Host "3. Add your website credentials" -ForegroundColor Gray
        Write-Host "4. Use the password generator for secure passwords" -ForegroundColor Gray
        Write-Host "5. Copy usernames/passwords with one click" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Security Notes:" -ForegroundColor Yellow
        Write-Host "- All passwords are encrypted with AES-256" -ForegroundColor Gray
        Write-Host "- Master password protects all your data" -ForegroundColor Gray
        Write-Host "- Data is stored locally in encrypted SQLite database" -ForegroundColor Gray
        Write-Host "- No internet connection required" -ForegroundColor Gray
        Write-Host ""
        Write-Host "The application window should now be visible on your screen." -ForegroundColor Green
        Write-Host "If you do not see it, check your taskbar or Alt+Tab to find it." -ForegroundColor Gray
    }

}
catch {
    Write-Host ""
    Write-Error "Error: $($_.Exception.Message)"
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. Install .NET 8.0 Desktop Runtime:" -ForegroundColor White
    Write-Host "   https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Alternative: Run console demo to verify core functionality:" -ForegroundColor White
    Write-Host "   cd ..\PasswordManagerDemo" -ForegroundColor Gray
    Write-Host "   dotnet run" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. Check Windows Event Viewer for detailed error information" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host ""
Write-Host "Press Enter to continue..." -ForegroundColor Gray
Read-Host
