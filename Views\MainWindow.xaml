<Window x:Class="PasswordManager.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:viewmodels="clr-namespace:PasswordManager.ViewModels"
        xmlns:local="clr-namespace:PasswordManager.Converters"
        Title="{Binding ApplicationTitle}"
        Height="700" Width="1000"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Loaded="Window_Loaded">

    <Window.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Custom Inverted Boolean to Visibility Converter -->
        <local:InvertedBooleanToVisibilityConverter x:Key="InvertedBooleanToVisibilityConverter"/>

        <!-- Inverted Boolean to Visibility Converter -->
        <Style x:Key="InvertedBooleanToVisibilityStyle" TargetType="FrameworkElement">
            <Setter Property="Visibility" Value="Visible"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding}" Value="True">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Drop Shadow Effect -->
        <DropShadowEffect x:Key="DropShadowEffect"
                          Color="Black"
                          Direction="315"
                          ShadowDepth="5"
                          Opacity="0.3"
                          BlurRadius="10"/>
    </Window.Resources>

    <Window.DataContext>
        <viewmodels:MainViewModel />
    </Window.DataContext>

    <Grid>
        <!-- Login Screen -->
        <Grid>
            <Grid.Style>
                <Style TargetType="Grid">
                    <Setter Property="Visibility" Value="Collapsed"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsAuthenticated}" Value="False">
                            <Setter Property="Visibility" Value="Visible"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Grid.Style>
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#FF2E3440" Offset="0"/>
                    <GradientStop Color="#FF3B4252" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <Border Background="White"
                    CornerRadius="10"
                    Padding="40"
                    MaxWidth="400"
                    MaxHeight="300"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Effect="{StaticResource DropShadowEffect}">

                <StackPanel>
                    <TextBlock Text="🔐 Password Manager"
                               FontSize="24"
                               FontWeight="Bold"
                               HorizontalAlignment="Center"
                               Margin="0,0,0,20"
                               Foreground="#FF2E3440"/>

                    <TextBlock Text="Enter your master password to continue:"
                               FontSize="14"
                               Margin="0,0,0,15"
                               TextWrapping="Wrap"/>

                    <PasswordBox x:Name="MasterPasswordBox"
                                 FontSize="14"
                                 Padding="10"
                                 Margin="0,0,0,15"
                                 PasswordChanged="MasterPasswordBox_PasswordChanged"/>

                    <CheckBox Content="Remember master password"
                              IsChecked="{Binding RememberMasterPassword}"
                              Margin="0,0,0,20"/>

                    <Button Content="Unlock"
                            Command="{Binding AuthenticateCommand}"
                            IsDefault="True"
                            Padding="15,8"
                            FontSize="14"
                            FontWeight="SemiBold"/>

                    <!-- Messages -->
                    <TextBlock Text="{Binding ErrorMessage}"
                               Foreground="Red"
                               Margin="0,10,0,0"
                               TextWrapping="Wrap"
                               Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                    <TextBlock Text="{Binding SuccessMessage}"
                               Foreground="Green"
                               Margin="0,10,0,0"
                               TextWrapping="Wrap"
                               Visibility="{Binding HasSuccess, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Main Application -->
        <Grid Visibility="{Binding IsAuthenticated, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Menu Bar -->
            <Menu Grid.Row="0" Background="#FF2E3440" Foreground="White">
                <MenuItem Header="_File">
                    <MenuItem Header="_Logout" Command="{Binding LogoutCommand}"/>
                    <Separator/>
                    <MenuItem Header="E_xit" Command="{Binding ExitApplicationCommand}"/>
                </MenuItem>
                <MenuItem Header="_Help">
                    <MenuItem Header="_About" Command="{Binding AboutCommand}"/>
                </MenuItem>
            </Menu>

            <!-- Tab Control -->
            <TabControl Grid.Row="1"
                        SelectedIndex="{Binding SelectedTabIndex}"
                        Background="White"
                        BorderThickness="0">

                <!-- Save Account Tab -->
                <TabItem Header="حفظ اكونت (Save Account)" FontSize="14" FontWeight="SemiBold">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Input Form -->
                        <Border Grid.Row="0"
                                Background="#FFF8F9FA"
                                BorderBrush="#FFCCCCCC"
                                BorderThickness="1"
                                CornerRadius="5"
                                Padding="20"
                                Margin="0,0,0,20">

                            <Grid DataContext="{Binding SaveAccountViewModel}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- Website Name -->
                                <Label Grid.Row="0" Grid.Column="0" Content="Website Name *:" FontWeight="SemiBold"/>
                                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding WebsiteName, UpdateSourceTrigger=PropertyChanged}"
                                         Margin="5" Padding="8"/>

                                <!-- Website URL -->
                                <Label Grid.Row="1" Grid.Column="0" Content="Website URL:" FontWeight="SemiBold"/>
                                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding WebsiteUrl, UpdateSourceTrigger=PropertyChanged}"
                                         Margin="5" Padding="8"/>

                                <!-- Username -->
                                <Label Grid.Row="2" Grid.Column="0" Content="Username *:" FontWeight="SemiBold"/>
                                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                                         Margin="5" Padding="8"/>

                                <!-- Password -->
                                <Label Grid.Row="3" Grid.Column="0" Content="Password *:" FontWeight="SemiBold"/>
                                <Grid Grid.Row="3" Grid.Column="1" Margin="5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0"
                                             Text="{Binding Password, UpdateSourceTrigger=PropertyChanged}"
                                             Padding="8"
                                             Visibility="{Binding IsPasswordVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                                    <PasswordBox Grid.Column="0"
                                                 x:Name="PasswordInput"
                                                 Padding="8"
                                                 Visibility="{Binding IsPasswordVisible, Converter={StaticResource InvertedBooleanToVisibilityConverter}}"
                                                 PasswordChanged="PasswordInput_PasswordChanged"/>

                                    <Button Grid.Column="1"
                                            Content="👁"
                                            Command="{Binding TogglePasswordVisibilityCommand}"
                                            Width="30" Height="30"
                                            Margin="2,0"/>

                                    <Button Grid.Column="2"
                                            Content="🎲"
                                            Command="{Binding GeneratePasswordCommand}"
                                            Width="30" Height="30"
                                            Margin="2,0"
                                            ToolTip="Generate secure password"/>
                                </Grid>

                                <!-- Notes -->
                                <Label Grid.Row="4" Grid.Column="0" Content="Notes:" FontWeight="SemiBold"/>
                                <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                         Margin="5" Padding="8" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                            </Grid>
                        </Border>

                        <!-- Action Buttons -->
                        <StackPanel Grid.Row="1"
                                    Orientation="Horizontal"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,20"
                                    DataContext="{Binding SaveAccountViewModel}">

                            <Button Content="Save Account"
                                    Command="{Binding SaveAccountCommand}"
                                    Visibility="{Binding IsEditMode, Converter={StaticResource InvertedBooleanToVisibilityConverter}}"
                                    Padding="15,8"
                                    Margin="5"/>

                            <Button Content="Update Account"
                                    Command="{Binding UpdateAccountCommand}"
                                    Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                    Padding="15,8"
                                    Margin="5"/>

                            <Button Content="Cancel"
                                    Command="{Binding CancelEditCommand}"
                                    Visibility="{Binding IsEditMode, Converter={StaticResource BooleanToVisibilityConverter}}"
                                    Padding="15,8"
                                    Margin="5"/>

                            <Button Content="Clear Form"
                                    Command="{Binding ClearFormCommand}"
                                    Padding="15,8"
                                    Margin="5"/>
                        </StackPanel>

                        <!-- Accounts List -->
                        <Border Grid.Row="2"
                                BorderBrush="#FFCCCCCC"
                                BorderThickness="1"
                                CornerRadius="5">

                            <Grid DataContext="{Binding SaveAccountViewModel}">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Search -->
                                <Border Grid.Row="0" Background="#FFF8F9FA" Padding="10">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Label Grid.Column="0" Content="Search:" VerticalAlignment="Center"/>
                                    <TextBox Grid.Column="1"
                                             Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                             Padding="8"
                                             Margin="5,0"/>
                                </Grid>
                                </Border>

                                <!-- Data Grid -->
                                <DataGrid Grid.Row="1"
                                          ItemsSource="{Binding Accounts}"
                                          SelectedItem="{Binding SelectedAccount}"
                                          AutoGenerateColumns="False"
                                          CanUserSortColumns="True"
                                          GridLinesVisibility="Horizontal"
                                          HeadersVisibility="Column">

                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="Website"
                                                            Binding="{Binding WebsiteName}"
                                                            Width="200"/>

                                        <DataGridTextColumn Header="Username"
                                                            Binding="{Binding Username}"
                                                            Width="200"/>

                                        <DataGridTextColumn Header="URL"
                                                            Binding="{Binding WebsiteUrl}"
                                                            Width="250"/>

                                        <DataGridTextColumn Header="Created"
                                                            Binding="{Binding CreatedDate, StringFormat=yyyy-MM-dd}"
                                                            Width="100"/>

                                        <DataGridTemplateColumn Header="Actions" Width="150">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Content="Edit"
                                                                Command="{Binding DataContext.EditAccountCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                Padding="5,2"
                                                                Margin="2"/>

                                                        <Button Content="Copy 👤"
                                                                Command="{Binding DataContext.CopyUsernameCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                Padding="5,2"
                                                                Margin="2"
                                                                ToolTip="Copy Username"/>

                                                        <Button Content="Copy 🔑"
                                                                Command="{Binding DataContext.CopyPasswordCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                Padding="5,2"
                                                                Margin="2"
                                                                ToolTip="Copy Password"/>

                                                        <Button Content="🗑"
                                                                Command="{Binding DataContext.DeleteAccountCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                                CommandParameter="{Binding}"
                                                                Padding="5,2"
                                                                Margin="2"
                                                                ToolTip="Delete Account"/>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </Border>
                    </Grid>
                </TabItem>

                <!-- Future Tab -->
                <TabItem Header="Future Features" FontSize="14" FontWeight="SemiBold">
                    <Grid Margin="20">
                        <Border Background="#FFF8F9FA"
                                BorderBrush="#FFCCCCCC"
                                BorderThickness="1"
                                CornerRadius="5"
                                Padding="40">

                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock Text="🚀 Coming Soon!"
                                           FontSize="32"
                                           FontWeight="Bold"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,20"/>

                                <TextBlock Text="This tab is reserved for future functionality."
                                           FontSize="16"
                                           HorizontalAlignment="Center"
                                           Margin="0,0,0,10"/>

                                <TextBlock Text="Possible features:"
                                           FontSize="14"
                                           FontWeight="SemiBold"
                                           HorizontalAlignment="Center"
                                           Margin="0,20,0,10"/>

                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="• Password strength analyzer" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Secure notes storage" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Data export/import" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Password expiration tracking" FontSize="12" Margin="0,2"/>
                                    <TextBlock Text="• Two-factor authentication codes" FontSize="12" Margin="0,2"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </Grid>
                </TabItem>
            </TabControl>

            <!-- Status Bar -->
            <StatusBar Grid.Row="2" Background="#FF2E3440" Foreground="White">
                <StatusBarItem>
                    <TextBlock Text="{Binding ErrorMessage}"
                               Foreground="LightCoral"
                               Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StatusBarItem>
                <StatusBarItem>
                    <TextBlock Text="{Binding SuccessMessage}"
                               Foreground="LightGreen"
                               Visibility="{Binding HasSuccess, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StatusBarItem>
                <StatusBarItem HorizontalAlignment="Right">
                    <TextBlock Text="Password Manager v1.0 - Secure &amp; Portable"/>
                </StatusBarItem>
            </StatusBar>
        </Grid>
    </Grid>
</Window>
