@echo off
REM Launch Password Manager - Simple Batch Launcher
REM This batch file provides a simple way to launch the Password Manager

echo.
echo ========================================
echo Password Manager - Simple Launcher
echo ========================================
echo.

REM Check if executable exists
if exist "bin\Release\net8.0-windows\win-x64\PasswordManager.exe" (
    echo [INFO] Found Password Manager executable
    echo [INFO] Starting Password Manager...
    echo.

    REM Launch the application
    start "" "bin\Release\net8.0-windows\win-x64\PasswordManager.exe"

    echo [SUCCESS] Password Manager launched!
    echo.
    echo The application window should appear shortly.
    echo If you don't see it, check your taskbar.
    echo.
    echo Features:
    echo - Master password protection
    echo - AES-256 encryption
    echo - Tabbed interface
    echo - Password generator
    echo - Local SQLite storage
    echo.
) else (
    echo [ERROR] Password Manager executable not found!
    echo.
    echo Please build the application first by running:
    echo   dotnet build PasswordManager-Modern.csproj --configuration Release
    echo.
    echo Or use the PowerShell launcher:
    echo   .\Start-PasswordManager.ps1
    echo.
    pause
    exit /b 1
)

echo Press any key to close this window...
pause >nul
