using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace PasswordManager.Services
{
    /// <summary>
    /// Performance monitoring service for tracking application metrics
    /// </summary>
    public static class PerformanceMonitor
    {
        private static readonly ConcurrentDictionary<string, PerformanceMetric> _metrics = new();
        private static readonly Timer _reportingTimer;
        private static long _totalMemoryAllocated = 0;
        private static long _totalOperations = 0;

        static PerformanceMonitor()
        {
            // Report metrics every 5 minutes
            _reportingTimer = new Timer(ReportMetrics, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// Performance metric data
        /// </summary>
        public class PerformanceMetric
        {
            public string Name { get; set; }
            public long TotalExecutions { get; set; }
            public long TotalDurationMs { get; set; }
            public long MinDurationMs { get; set; } = long.MaxValue;
            public long MaxDurationMs { get; set; }
            public double AverageDurationMs => TotalExecutions > 0 ? (double)TotalDurationMs / TotalExecutions : 0;
            public DateTime LastExecuted { get; set; }
            public long MemoryUsedBytes { get; set; }
        }

        /// <summary>
        /// Disposable performance tracker for measuring operation duration
        /// </summary>
        public sealed class PerformanceTracker : IDisposable
        {
            private readonly string _operationName;
            private readonly Stopwatch _stopwatch;
            private readonly long _initialMemory;
            private bool _disposed = false;

            public PerformanceTracker(string operationName)
            {
                _operationName = operationName;
                _initialMemory = GC.GetTotalMemory(false);
                _stopwatch = Stopwatch.StartNew();
            }

            public void Dispose()
            {
                if (!_disposed)
                {
                    _stopwatch.Stop();
                    var finalMemory = GC.GetTotalMemory(false);
                    var memoryUsed = Math.Max(0, finalMemory - _initialMemory);

                    RecordMetric(_operationName, _stopwatch.ElapsedMilliseconds, memoryUsed);
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// Starts tracking performance for an operation
        /// </summary>
        public static PerformanceTracker StartTracking(string operationName)
        {
            return new PerformanceTracker(operationName);
        }

        /// <summary>
        /// Records a performance metric
        /// </summary>
        public static void RecordMetric(string operationName, long durationMs, long memoryUsed = 0)
        {
            _metrics.AddOrUpdate(operationName,
                new PerformanceMetric
                {
                    Name = operationName,
                    TotalExecutions = 1,
                    TotalDurationMs = durationMs,
                    MinDurationMs = durationMs,
                    MaxDurationMs = durationMs,
                    LastExecuted = DateTime.Now,
                    MemoryUsedBytes = memoryUsed
                },
                (key, existing) =>
                {
                    existing.TotalExecutions++;
                    existing.TotalDurationMs += durationMs;
                    existing.MinDurationMs = Math.Min(existing.MinDurationMs, durationMs);
                    existing.MaxDurationMs = Math.Max(existing.MaxDurationMs, durationMs);
                    existing.LastExecuted = DateTime.Now;
                    existing.MemoryUsedBytes += memoryUsed;
                    return existing;
                });

            Interlocked.Add(ref _totalMemoryAllocated, memoryUsed);
            Interlocked.Increment(ref _totalOperations);
        }

        /// <summary>
        /// Gets performance metrics for a specific operation
        /// </summary>
        public static PerformanceMetric GetMetric(string operationName)
        {
            return _metrics.TryGetValue(operationName, out var metric) ? metric : null;
        }

        /// <summary>
        /// Gets all performance metrics
        /// </summary>
        public static ConcurrentDictionary<string, PerformanceMetric> GetAllMetrics()
        {
            return new ConcurrentDictionary<string, PerformanceMetric>(_metrics);
        }

        /// <summary>
        /// Gets current system performance information
        /// </summary>
        public static SystemPerformanceInfo GetSystemPerformance()
        {
            var process = Process.GetCurrentProcess();

            return new SystemPerformanceInfo
            {
                WorkingSetMemoryMB = process.WorkingSet64 / (1024 * 1024),
                PrivateMemoryMB = process.PrivateMemorySize64 / (1024 * 1024),
                VirtualMemoryMB = process.VirtualMemorySize64 / (1024 * 1024),
                GCTotalMemoryMB = GC.GetTotalMemory(false) / (1024 * 1024),
                ThreadCount = process.Threads.Count,
                HandleCount = process.HandleCount,
                TotalProcessorTimeMs = (long)process.TotalProcessorTime.TotalMilliseconds,
                TotalOperations = _totalOperations,
                TotalMemoryAllocatedMB = _totalMemoryAllocated / (1024 * 1024)
            };
        }

        /// <summary>
        /// System performance information
        /// </summary>
        public class SystemPerformanceInfo
        {
            public long WorkingSetMemoryMB { get; set; }
            public long PrivateMemoryMB { get; set; }
            public long VirtualMemoryMB { get; set; }
            public long GCTotalMemoryMB { get; set; }
            public int ThreadCount { get; set; }
            public int HandleCount { get; set; }
            public long TotalProcessorTimeMs { get; set; }
            public long TotalOperations { get; set; }
            public long TotalMemoryAllocatedMB { get; set; }
        }

        /// <summary>
        /// Clears all performance metrics
        /// </summary>
        public static void ClearMetrics()
        {
            _metrics.Clear();
            Interlocked.Exchange(ref _totalMemoryAllocated, 0);
            Interlocked.Exchange(ref _totalOperations, 0);
        }

        /// <summary>
        /// Forces garbage collection and reports memory usage
        /// </summary>
        public static void ForceGarbageCollection()
        {
            var beforeGC = GC.GetTotalMemory(false);

            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            var afterGC = GC.GetTotalMemory(false);
            var freedMemory = beforeGC - afterGC;

            LoggingService.LogInfo($"Garbage collection completed. Freed {freedMemory / (1024 * 1024):F2} MB of memory");
        }

        /// <summary>
        /// Reports performance metrics to the logging service
        /// </summary>
        private static void ReportMetrics(object state)
        {
            try
            {
                var systemInfo = GetSystemPerformance();

                LoggingService.LogInfo($"PERFORMANCE REPORT - Memory: {systemInfo.WorkingSetMemoryMB}MB, " +
                    $"Threads: {systemInfo.ThreadCount}, Operations: {systemInfo.TotalOperations}");

                // Report top 5 slowest operations
                var topOperations = _metrics.Values
                    .OrderByDescending(m => m.AverageDurationMs)
                    .Take(5);

                foreach (var metric in topOperations)
                {
                    LoggingService.LogInfo($"PERFORMANCE METRIC - {metric.Name}: " +
                        $"Avg: {metric.AverageDurationMs:F2}ms, " +
                        $"Executions: {metric.TotalExecutions}, " +
                        $"Memory: {metric.MemoryUsedBytes / (1024 * 1024):F2}MB");
                }

                // Check for memory pressure
                if (systemInfo.WorkingSetMemoryMB > 500) // More than 500MB
                {
                    LoggingService.LogWarning($"High memory usage detected: {systemInfo.WorkingSetMemoryMB}MB");

                    // Force garbage collection if memory usage is very high
                    if (systemInfo.WorkingSetMemoryMB > 1000) // More than 1GB
                    {
                        ForceGarbageCollection();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to report performance metrics", ex);
            }
        }

        /// <summary>
        /// Measures the execution time of an action
        /// </summary>
        public static T MeasureExecution<T>(string operationName, Func<T> action)
        {
            using var tracker = StartTracking(operationName);
            return action();
        }

        /// <summary>
        /// Measures the execution time of an async action
        /// </summary>
        public static async Task<T> MeasureExecutionAsync<T>(string operationName, Func<Task<T>> action)
        {
            using var tracker = StartTracking(operationName);
            return await action();
        }

        /// <summary>
        /// Measures the execution time of an action without return value
        /// </summary>
        public static void MeasureExecution(string operationName, Action action)
        {
            using var tracker = StartTracking(operationName);
            action();
        }

        /// <summary>
        /// Measures the execution time of an async action without return value
        /// </summary>
        public static async Task MeasureExecutionAsync(string operationName, Func<Task> action)
        {
            using var tracker = StartTracking(operationName);
            await action();
        }
    }
}
